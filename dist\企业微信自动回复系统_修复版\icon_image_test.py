#!/usr/bin/env python3
# encoding:utf-8
"""
图标和图片显示测试程序
"""

import os
import sys
from pathlib import Path
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QPushButton
from PyQt5.QtGui import QPixmap, QIcon
from PyQt5.QtCore import Qt

class IconTestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("图标和图片显示测试")
        self.setGeometry(100, 100, 600, 500)
        
        # 设置窗口图标
        self.setup_window_icon()
        
        # 创建界面
        self.setup_ui()
    
    def setup_window_icon(self):
        """设置窗口图标"""
        print("🔧 设置窗口图标...")
        
        # 获取当前目录
        if hasattr(sys, '_MEIPASS'):
            # PyInstaller打包后的临时目录
            current_dir = Path(sys._MEIPASS)
            print(f"   PyInstaller模式，临时目录: {current_dir}")
        else:
            # 开发模式
            current_dir = Path(__file__).parent
            print(f"   开发模式，当前目录: {current_dir}")
        
        # 尝试多个可能的图标路径
        icon_paths = [
            current_dir / "2.ico",
            Path(sys.executable).parent / "2.ico",
            Path.cwd() / "2.ico"
        ]
        
        icon_set = False
        for icon_path in icon_paths:
            print(f"   尝试图标路径: {icon_path}")
            if icon_path.exists():
                try:
                    icon = QIcon(str(icon_path))
                    if not icon.isNull():
                        self.setWindowIcon(icon)
                        print(f"   ✅ 窗口图标设置成功: {icon_path}")
                        icon_set = True
                        break
                except Exception as e:
                    print(f"   ❌ 图标加载失败: {e}")
            else:
                print(f"   ❌ 图标文件不存在: {icon_path}")
        
        if not icon_set:
            print("   ⚠️ 使用默认图标")
    
    def setup_ui(self):
        """设置用户界面"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 标题
        title_label = QLabel("图标和图片显示测试")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("font-size: 18px; font-weight: bold; margin: 10px;")
        layout.addWidget(title_label)
        
        # 测试12.jpg
        self.test_image_display(layout, "12.jpg", "技术支持联系方式图片")
        
        # 测试33.png
        self.test_image_display(layout, "33.png", "捐赠二维码图片")
        
        # 路径信息
        info_label = QLabel()
        info_text = f"""
当前工作目录: {Path.cwd()}
程序执行路径: {Path(sys.executable).parent}
脚本文件路径: {Path(__file__).parent if '__file__' in globals() else '未知'}
"""
        if hasattr(sys, '_MEIPASS'):
            info_text += f"PyInstaller临时目录: {sys._MEIPASS}\n"
        
        info_label.setText(info_text)
        info_label.setStyleSheet("font-size: 10px; color: gray; margin: 10px;")
        layout.addWidget(info_label)
        
        # 关闭按钮
        close_btn = QPushButton("关闭")
        close_btn.clicked.connect(self.close)
        layout.addWidget(close_btn)
    
    def test_image_display(self, layout, filename, description):
        """测试图片显示"""
        print(f"🖼️ 测试图片: {filename}")
        
        # 创建图片标签
        image_label = QLabel()
        image_label.setAlignment(Qt.AlignCenter)
        image_label.setStyleSheet("border: 1px solid #ccc; margin: 5px; padding: 10px; min-height: 150px;")
        
        # 获取当前目录
        if hasattr(sys, '_MEIPASS'):
            current_dir = Path(sys._MEIPASS)
        else:
            current_dir = Path(__file__).parent
        
        # 尝试多个可能的图片路径
        image_paths = [
            current_dir / filename,
            Path(sys.executable).parent / filename,
            Path.cwd() / filename
        ]
        
        image_loaded = False
        for image_path in image_paths:
            print(f"   尝试图片路径: {image_path}")
            if image_path.exists():
                try:
                    pixmap = QPixmap(str(image_path))
                    if not pixmap.isNull():
                        # 缩放图片
                        scaled_pixmap = pixmap.scaled(200, 150, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                        image_label.setPixmap(scaled_pixmap)
                        image_label.setText(f"✅ {description}\n({filename})")
                        print(f"   ✅ 图片加载成功: {image_path}")
                        image_loaded = True
                        break
                except Exception as e:
                    print(f"   ❌ 图片加载失败: {e}")
            else:
                print(f"   ❌ 图片文件不存在: {image_path}")
        
        if not image_loaded:
            image_label.setText(f"❌ {description}\n({filename} 未找到)")
            print(f"   ❌ {filename} 加载失败")
        
        layout.addWidget(image_label)

def main():
    """主函数"""
    print("🧪 图标和图片显示测试程序启动")
    print("=" * 40)
    
    app = QApplication(sys.argv)
    window = IconTestWindow()
    window.show()
    
    print("✅ 测试窗口已显示")
    print("💡 请检查:")
    print("   1. 窗口标题栏是否显示正确图标")
    print("   2. 任务栏中的程序图标是否正确")
    print("   3. 窗口中的图片是否正确显示")
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
