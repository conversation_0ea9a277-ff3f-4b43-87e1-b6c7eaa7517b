#!/usr/bin/env python3
# encoding:utf-8
"""
修复PyInstaller打包后的路径解析问题
"""

import os
import sys
import shutil
from pathlib import Path

def create_resource_helper():
    """创建资源路径解析辅助模块"""
    print("📝 创建资源路径解析辅助模块...")
    
    helper_content = '''#!/usr/bin/env python3
# encoding:utf-8
"""
资源路径解析辅助模块
解决PyInstaller打包后的路径问题
"""

import os
import sys
from pathlib import Path

def get_resource_path(relative_path):
    """
    获取资源文件的绝对路径
    
    Args:
        relative_path (str): 相对路径，如 "2.ico" 或 "12.jpg"
    
    Returns:
        str: 资源文件的绝对路径
    """
    try:
        # PyInstaller打包后的情况
        if hasattr(sys, '_MEIPASS'):
            # 在临时目录中查找
            base_path = sys._MEIPASS
            resource_path = os.path.join(base_path, relative_path)
            if os.path.exists(resource_path):
                return resource_path
        
        # 开发环境或其他情况，尝试多个可能的路径
        possible_paths = [
            # 当前工作目录
            os.path.join(os.getcwd(), relative_path),
            
            # exe文件所在目录
            os.path.join(os.path.dirname(sys.executable), relative_path),
            
            # 脚本文件所在目录的上级目录（开发环境）
            os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), relative_path),
            
            # 脚本文件所在目录
            os.path.join(os.path.dirname(os.path.abspath(__file__)), relative_path),
        ]
        
        # 尝试每个可能的路径
        for path in possible_paths:
            if os.path.exists(path):
                return path
        
        # 如果都找不到，返回相对路径（让调用者处理）
        return relative_path
        
    except Exception as e:
        print(f"获取资源路径失败: {e}")
        return relative_path

def get_icon_path():
    """获取程序图标路径"""
    return get_resource_path("2.ico")

def get_contact_image_path():
    """获取联系方式图片路径"""
    return get_resource_path("12.jpg")

def get_donate_image_path():
    """获取捐赠二维码路径"""
    return get_resource_path("33.png")

def debug_paths():
    """调试路径信息"""
    print("🔍 路径调试信息:")
    print(f"   当前工作目录: {os.getcwd()}")
    print(f"   sys.executable: {sys.executable}")
    print(f"   __file__: {__file__ if '__file__' in globals() else '未定义'}")
    
    if hasattr(sys, '_MEIPASS'):
        print(f"   PyInstaller临时目录: {sys._MEIPASS}")
    
    print("🔍 资源文件路径:")
    print(f"   图标文件: {get_icon_path()}")
    print(f"   联系方式图片: {get_contact_image_path()}")
    print(f"   捐赠二维码: {get_donate_image_path()}")
    
    print("🔍 文件存在性检查:")
    for name, path in [
        ("图标文件", get_icon_path()),
        ("联系方式图片", get_contact_image_path()),
        ("捐赠二维码", get_donate_image_path())
    ]:
        exists = os.path.exists(path)
        print(f"   {name}: {'✅' if exists else '❌'} {path}")

if __name__ == "__main__":
    debug_paths()
'''
    
    # 保存到common目录
    common_dir = Path("common")
    if not common_dir.exists():
        common_dir.mkdir()
    
    resource_helper_path = common_dir / "resource_helper.py"
    with open(resource_helper_path, 'w', encoding='utf-8') as f:
        f.write(helper_content)
    
    print(f"✅ 资源路径辅助模块创建完成: {resource_helper_path}")

def fix_main_window_icon():
    """修复主窗口图标设置"""
    print("🔧 修复主窗口图标设置...")
    
    main_window_file = Path("gui/main_window.py")
    if not main_window_file.exists():
        print("❌ gui/main_window.py 不存在")
        return False
    
    # 读取原文件
    with open(main_window_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 添加导入
    if "from common.resource_helper import get_icon_path" not in content:
        # 在其他导入后添加
        import_pos = content.find("from PyQt5.QtCore import")
        if import_pos != -1:
            # 找到这行的结尾
            line_end = content.find('\n', import_pos)
            new_import = "\nfrom common.resource_helper import get_icon_path"
            content = content[:line_end] + new_import + content[line_end:]
    
    # 替换图标设置函数
    old_function = '''    def set_window_icon(self):
        """设置窗口图标"""
        try:
            # 获取图标文件路径
            icon_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "2.ico")
            if os.path.exists(icon_path):
                icon = QIcon(icon_path)
                self.setWindowIcon(icon)
                print(f"窗口图标设置成功: {icon_path}")
            else:
                print(f"图标文件不存在: {icon_path}")
        except Exception as e:
            print(f"设置窗口图标失败: {e}")'''
    
    new_function = '''    def set_window_icon(self):
        """设置窗口图标"""
        try:
            # 使用资源路径辅助函数获取图标路径
            icon_path = get_icon_path()
            if os.path.exists(icon_path):
                icon = QIcon(icon_path)
                self.setWindowIcon(icon)
                print(f"窗口图标设置成功: {icon_path}")
            else:
                print(f"图标文件不存在: {icon_path}")
                # 尝试创建默认图标
                self.create_default_icon()
        except Exception as e:
            print(f"设置窗口图标失败: {e}")
            self.create_default_icon()
    
    def create_default_icon(self):
        """创建默认图标"""
        try:
            from PyQt5.QtGui import QPixmap, QPainter, QBrush, QColor
            pixmap = QPixmap(32, 32)
            pixmap.fill()
            
            painter = QPainter(pixmap)
            painter.setRenderHint(QPainter.Antialiasing)
            painter.setBrush(QBrush(QColor(0, 120, 215)))
            painter.drawEllipse(4, 4, 24, 24)
            painter.setPen(QColor(255, 255, 255))
            painter.drawText(12, 20, "W")
            painter.end()
            
            icon = QIcon(pixmap)
            self.setWindowIcon(icon)
            print("✅ 使用默认图标")
        except Exception as e:
            print(f"创建默认图标失败: {e}")'''
    
    # 替换函数
    content = content.replace(old_function, new_function)
    
    # 保存修改后的文件
    with open(main_window_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ 主窗口图标设置已修复")
    return True

def fix_help_window_image():
    """修复帮助窗口图片加载"""
    print("🔧 修复帮助窗口图片加载...")
    
    help_window_file = Path("gui/help_window.py")
    if not help_window_file.exists():
        print("❌ gui/help_window.py 不存在")
        return False
    
    # 读取原文件
    with open(help_window_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 添加导入
    if "from common.resource_helper import get_contact_image_path" not in content:
        # 在其他导入后添加
        import_pos = content.find("from PyQt5.QtCore import")
        if import_pos != -1:
            line_end = content.find('\n', import_pos)
            new_import = "\nfrom common.resource_helper import get_contact_image_path"
            content = content[:line_end] + new_import + content[line_end:]
    
    # 替换图片加载逻辑
    old_code = '''        # 尝试加载图片
        try:
            import os
            image_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "12.jpg")
            if os.path.exists(image_path):'''
    
    new_code = '''        # 尝试加载图片
        try:
            import os
            image_path = get_contact_image_path()
            if os.path.exists(image_path):'''
    
    content = content.replace(old_code, new_code)
    
    # 保存修改后的文件
    with open(help_window_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ 帮助窗口图片加载已修复")
    return True

def fix_donate_window_image():
    """修复捐赠窗口图片加载"""
    print("🔧 修复捐赠窗口图片加载...")
    
    donate_window_file = Path("gui/donate_window.py")
    if not donate_window_file.exists():
        print("❌ gui/donate_window.py 不存在")
        return False
    
    # 读取原文件
    with open(donate_window_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 添加导入
    if "from common.resource_helper import get_donate_image_path" not in content:
        # 在其他导入后添加
        import_pos = content.find("from PyQt5.QtCore import")
        if import_pos != -1:
            line_end = content.find('\n', import_pos)
            new_import = "\nfrom common.resource_helper import get_donate_image_path"
            content = content[:line_end] + new_import + content[line_end:]
    
    # 替换图片加载逻辑
    old_code = '''            image_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "33.png")'''
    new_code = '''            image_path = get_donate_image_path()'''
    
    content = content.replace(old_code, new_code)
    
    # 保存修改后的文件
    with open(donate_window_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ 捐赠窗口图片加载已修复")
    return True

def fix_system_tray_icon():
    """修复系统托盘图标"""
    print("🔧 修复系统托盘图标...")
    
    system_tray_file = Path("gui/system_tray.py")
    if not system_tray_file.exists():
        print("❌ gui/system_tray.py 不存在")
        return False
    
    # 读取原文件
    with open(system_tray_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 添加导入
    if "from common.resource_helper import get_icon_path" not in content:
        # 在其他导入后添加
        import_pos = content.find("from PyQt5.QtCore import")
        if import_pos != -1:
            line_end = content.find('\n', import_pos)
            new_import = "\nfrom common.resource_helper import get_icon_path"
            content = content[:line_end] + new_import + content[line_end:]
    
    # 替换托盘图标设置逻辑
    old_code = '''            # 尝试使用2.ico文件
            icon_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "2.ico")'''
    new_code = '''            # 尝试使用2.ico文件
            icon_path = get_icon_path()'''
    
    content = content.replace(old_code, new_code)
    
    # 保存修改后的文件
    with open(system_tray_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ 系统托盘图标已修复")
    return True

def create_fixed_spec():
    """创建修复后的spec文件"""
    print("📝 创建修复后的spec文件...")
    
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

import os
from pathlib import Path

# 项目根目录
project_root = os.path.dirname(os.path.abspath(SPEC))

# 数据文件 - 确保图片文件被正确包含
datas = []

# 添加配置文件
config_files = ['config-template.json', 'config-gui.json']
for config_file in config_files:
    config_path = os.path.join(project_root, config_file)
    if os.path.exists(config_path):
        datas.append((config_path, '.'))

# 添加图片文件 - 关键修复：确保图片文件在根目录
image_files = ['2.ico', '12.jpg', '33.png']
for image_file in image_files:
    image_path = os.path.join(project_root, image_file)
    if os.path.exists(image_path):
        datas.append((image_path, '.'))  # 放在根目录
        print(f"添加图片文件到根目录: {image_file}")
    else:
        print(f"警告: 图片文件不存在: {image_path}")

# 手动添加ntwork目录
ntwork_path = os.path.join(project_root, 'ntwork')
if os.path.exists(ntwork_path):
    for root, dirs, files in os.walk(ntwork_path):
        for file in files:
            if not file.endswith('.pyc') and '__pycache__' not in root:
                src_path = os.path.join(root, file)
                rel_path = os.path.relpath(src_path, project_root)
                dest_path = os.path.dirname(rel_path)
                datas.append((src_path, dest_path))

# 包含所有必要的隐藏导入
hiddenimports = [
    # PyQt5相关
    'PyQt5.QtCore',
    'PyQt5.QtGui', 
    'PyQt5.QtWidgets',
    'PyQt5.sip',
    'PyQt5.QtPrintSupport',
    
    # PIL/Pillow相关
    'PIL',
    'PIL.Image',
    'PIL.ImageTk',
    'PIL.ImageDraw',
    'PIL.ImageFont',
    'PIL.ImageFilter',
    'PIL.ImageEnhance',
    'PIL.ImageOps',
    'PIL.ImageChops',
    'PIL.ImageStat',
    'PIL.ImageColor',
    'PIL.ImageFile',
    'PIL.ImageSequence',
    'PIL.ImageShow',
    'PIL.ImageWin',
    'PIL.ImageGrab',
    'PIL.ImagePath',
    'PIL.ImageQt',
    'PIL.ImageCms',
    'PIL.ImageMath',
    'PIL.ImageMode',
    'PIL.ImagePalette',
    'PIL.ImageTransform',
    'PIL._imaging',
    'PIL._imagingft',
    'PIL._imagingmath',
    'PIL._imagingmorph',
    'PIL._imagingcms',
    'PIL._webp',
    'PIL._tkinter_finder',
    
    # ntwork相关
    'ntwork',
    'ntwork.core',
    'ntwork.core.wework',
    'ntwork.core.mgr',
    'ntwork.utils',
    'ntwork.utils.logger',
    'ntwork.utils.singleton',
    'ntwork.utils.xdg',
    'ntwork.const',
    'ntwork.const.notify_type',
    'ntwork.const.send_type',
    'ntwork.exception',
    'ntwork.wc',
    
    # ntwork依赖
    'pilk',
    'pyee',
    'pyee.base',
    'pyee.asyncio',
    'websockets',
    'websockets.client',
    'websockets.server',
    'aiohttp',
    'aiohttp.client',
    'aiohttp.web',
    
    # 应用程序模块
    'gui.main_window',
    'gui.controllers',
    'gui.config_loader',
    'gui.managers',
    'gui.system_tray',
    'gui.help_window',
    'gui.donate_window',
    'config',
    'common.log',
    'common.utils',
    'common.singleton',
    'common.resource_helper',  # 新增的资源辅助模块
    'bridge.bridge',
    'bridge.context',
    'bridge.reply',
    'bot.bot_factory',
    'channel.channel_factory',
    'plugins.plugin_manager',
    
    # 其他依赖
    'requests',
    'json',
    'threading',
    'queue',
    'sqlite3',
    'configparser',
    'logging',
    'ctypes',
    'ctypes.wintypes',
    'win32api',
    'win32con',
    'win32gui',
    'pywintypes',
    'asyncio',
    'typing_extensions',
    'dataclasses',
]

# 排除模块
excludes = [
    'matplotlib',
    'scipy',
    'tensorflow',
    'torch',
    'tkinter',
    'pygame',
    'voice',
]

block_cipher = None

a = Analysis(
    ['gui_app.py'],
    pathex=[project_root],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=excludes,
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

# 确保图标文件存在
icon_path = os.path.join(project_root, '2.ico')
if not os.path.exists(icon_path):
    print(f"警告: 图标文件不存在: {icon_path}")
    icon_path = None

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='企业微信自动回复系统_路径修复版',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=False,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=icon_path,
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=False,
    upx_exclude=[],
    name='企业微信自动回复系统_路径修复版',
)
'''
    
    with open('path_fix_pack.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✅ 路径修复版spec文件创建完成")

def main():
    """主函数"""
    print("🔧 PyInstaller路径解析问题修复工具")
    print("=" * 60)
    
    # 创建资源路径辅助模块
    create_resource_helper()
    
    # 修复各个模块的路径问题
    fix_main_window_icon()
    fix_help_window_image()
    fix_donate_window_image()
    fix_system_tray_icon()
    
    # 创建修复后的spec文件
    create_fixed_spec()
    
    print("\n" + "=" * 60)
    print("🎉 路径修复完成！")
    print("=" * 60)
    print("💡 下一步操作:")
    print("1. 运行: pack_env\\Scripts\\python.exe -m PyInstaller path_fix_pack.spec")
    print("2. 运行: python fix_qt_plugins.py")
    print("3. 测试: 运行生成的exe文件")
    print("4. 检查图标和图片是否正确显示")
    print("\n✅ 这次应该能完全解决路径解析问题！")

if __name__ == "__main__":
    main()
