# -*- mode: python ; coding: utf-8 -*-
# 简单测试应用spec文件

import os
from pathlib import Path

project_root = os.path.dirname(os.path.abspath(SPEC))

# 最小化的数据文件
datas = []

# 隐藏导入 - 只包含必要的
hiddenimports = [
    'PyQt5.QtCore', 'PyQt5.QtGui', 'PyQt5.QtWidgets', 'PyQt5.sip',
    'common.embedded_resources',
    'base64', 'tempfile',
]

excludes = ['matplotlib', 'scipy', 'tensorflow', 'torch', 'tkinter', 'pygame', 'voice', 'ntwork', 'pilk']

block_cipher = None

a = Analysis(
    ['simple_test_app.py'],
    pathex=[project_root],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=excludes,
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

icon_path = os.path.join(project_root, '2.ico')

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='简单测试应用',
    debug=True,
    bootloader_ignore_signals=False,
    strip=False,
    upx=False,
    console=True,  # 显示控制台
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=icon_path if os.path.exists(icon_path) else None,
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=False,
    upx_exclude=[],
    name='简单测试应用',
)
