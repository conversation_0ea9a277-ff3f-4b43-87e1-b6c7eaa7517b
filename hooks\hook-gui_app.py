# PyInstaller hook for ensuring image resources are included

from PyInstaller.utils.hooks import collect_data_files
import os

# 获取当前目录
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)

# 定义要包含的资源文件
resource_files = ['2.ico', '12.jpg', '33.png']

# 创建datas列表
datas = []
for resource_file in resource_files:
    resource_path = os.path.join(project_root, resource_file)
    if os.path.exists(resource_path):
        datas.append((resource_path, '.'))
        print(f"Hook添加资源文件: {resource_file}")

print(f"Hook包含了 {len(datas)} 个资源文件")
