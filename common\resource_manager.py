#!/usr/bin/env python3
# encoding:utf-8
"""
增强的资源管理器 - 带详细调试信息
"""

import os
import sys
from pathlib import Path

class EnhancedResourceManager:
    """增强的资源管理器类"""
    
    @staticmethod
    def debug_environment():
        """调试环境信息"""
        print("🔍 环境调试信息:")
        print(f"   sys.frozen: {getattr(sys, 'frozen', False)}")
        print(f"   sys._MEIPASS: {getattr(sys, '_MEIPASS', '未定义')}")
        print(f"   sys.executable: {sys.executable}")
        print(f"   os.getcwd(): {os.getcwd()}")
        print(f"   __file__: {__file__ if '__file__' in globals() else '未定义'}")
        
        if hasattr(sys, '_MEIPASS'):
            print(f"   PyInstaller临时目录内容:")
            temp_dir = Path(sys._MEIPASS)
            if temp_dir.exists():
                for item in temp_dir.iterdir():
                    if item.name in ['2.ico', '12.jpg', '33.png']:
                        print(f"     ✅ {item.name}: {item.stat().st_size} 字节")
    
    @staticmethod
    def get_all_possible_paths(filename):
        """获取所有可能的文件路径"""
        paths = []
        
        # PyInstaller临时目录
        if hasattr(sys, '_MEIPASS'):
            paths.append(os.path.join(sys._MEIPASS, filename))
            paths.append(os.path.join(sys._MEIPASS, 'images', filename))
        
        # exe文件所在目录
        exe_dir = os.path.dirname(sys.executable)
        paths.append(os.path.join(exe_dir, filename))
        paths.append(os.path.join(exe_dir, 'images', filename))
        
        # 当前工作目录
        paths.append(os.path.join(os.getcwd(), filename))
        paths.append(os.path.join(os.getcwd(), 'images', filename))
        
        # 脚本文件目录（开发环境）
        if '__file__' in globals():
            script_dir = os.path.dirname(os.path.abspath(__file__))
            paths.append(os.path.join(script_dir, filename))
            paths.append(os.path.join(os.path.dirname(script_dir), filename))
        
        return paths
    
    @staticmethod
    def get_resource_path(filename):
        """获取资源文件路径，带详细调试"""
        print(f"🔍 查找文件: {filename}")
        
        paths = EnhancedResourceManager.get_all_possible_paths(filename)
        
        for i, path in enumerate(paths):
            exists = os.path.exists(path)
            print(f"   路径{i+1}: {'✅' if exists else '❌'} {path}")
            if exists:
                size = os.path.getsize(path)
                print(f"          文件大小: {size} 字节")
                return path
        
        print(f"   ❌ 所有路径都未找到文件: {filename}")
        return paths[0] if paths else filename
    
    @staticmethod
    def get_icon_path():
        """获取程序图标路径"""
        return EnhancedResourceManager.get_resource_path("2.ico")
    
    @staticmethod
    def get_contact_image_path():
        """获取联系方式图片路径"""
        return EnhancedResourceManager.get_resource_path("12.jpg")
    
    @staticmethod
    def get_donate_image_path():
        """获取捐赠二维码路径"""
        return EnhancedResourceManager.get_resource_path("33.png")

# 便捷函数
def get_icon_path():
    return EnhancedResourceManager.get_icon_path()

def get_contact_image_path():
    return EnhancedResourceManager.get_contact_image_path()

def get_donate_image_path():
    return EnhancedResourceManager.get_donate_image_path()

def debug_resources():
    """调试所有资源"""
    EnhancedResourceManager.debug_environment()
    print("\n🔍 资源文件检查:")
    get_icon_path()
    get_contact_image_path()
    get_donate_image_path()

if __name__ == "__main__":
    debug_resources()
