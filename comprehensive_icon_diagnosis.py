#!/usr/bin/env python3
# encoding:utf-8
"""
全面的图标和图片显示问题诊断工具
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

def diagnose_source_files():
    """诊断源文件情况"""
    print("🔍 第一步：诊断源文件情况")
    print("=" * 50)
    
    files_to_check = {
        "2.ico": "程序图标文件",
        "12.jpg": "技术支持联系方式图片",
        "33.png": "捐赠二维码图片"
    }
    
    all_files_ok = True
    for filename, description in files_to_check.items():
        file_path = Path(filename)
        if file_path.exists():
            size = file_path.stat().st_size
            print(f"✅ {filename} ({description}): {size:,} 字节")
            
            # 检查文件是否损坏
            if filename.endswith('.ico'):
                if size < 1000:
                    print(f"   ⚠️ 图标文件可能太小")
                    all_files_ok = False
            elif filename.endswith(('.jpg', '.png')):
                if size < 5000:
                    print(f"   ⚠️ 图片文件可能太小")
                    all_files_ok = False
        else:
            print(f"❌ {filename} ({description}): 文件不存在")
            all_files_ok = False
    
    return all_files_ok

def diagnose_dist_directories():
    """诊断dist目录中的文件情况"""
    print("\n🔍 第二步：诊断dist目录文件情况")
    print("=" * 50)
    
    dist_base = Path("dist")
    if not dist_base.exists():
        print("❌ dist目录不存在")
        return False
    
    dist_dirs = [d for d in dist_base.iterdir() if d.is_dir()]
    if not dist_dirs:
        print("❌ dist目录中没有找到任何子目录")
        return False
    
    print(f"📁 找到 {len(dist_dirs)} 个dist子目录:")
    
    files_to_check = ["2.ico", "12.jpg", "33.png"]
    
    for dist_dir in dist_dirs:
        print(f"\n📂 检查目录: {dist_dir.name}")
        
        # 检查exe文件
        exe_files = list(dist_dir.glob("*.exe"))
        if exe_files:
            exe_file = exe_files[0]
            size = exe_file.stat().st_size / (1024*1024)
            print(f"   🎯 EXE文件: {exe_file.name} ({size:.1f} MB)")
        else:
            print("   ❌ 未找到exe文件")
            continue
        
        # 检查图片文件
        for filename in files_to_check:
            file_path = dist_dir / filename
            if file_path.exists():
                size = file_path.stat().st_size
                print(f"   ✅ {filename}: {size:,} 字节")
            else:
                print(f"   ❌ {filename}: 缺失")
        
        # 检查Qt配置
        qt_conf = dist_dir / "qt.conf"
        platforms_dir = dist_dir / "platforms"
        if qt_conf.exists() and platforms_dir.exists():
            print(f"   ✅ Qt配置: qt.conf + platforms目录")
        else:
            print(f"   ❌ Qt配置: 缺失")
    
    return True

def diagnose_icon_embedding():
    """诊断图标嵌入情况"""
    print("\n🔍 第三步：诊断exe文件图标嵌入情况")
    print("=" * 50)
    
    dist_base = Path("dist")
    exe_files = []
    
    for dist_dir in dist_base.iterdir():
        if dist_dir.is_dir():
            for exe_file in dist_dir.glob("*.exe"):
                exe_files.append(exe_file)
    
    if not exe_files:
        print("❌ 未找到任何exe文件")
        return False
    
    for exe_file in exe_files:
        print(f"\n🎯 检查exe文件: {exe_file}")
        
        # 检查文件大小
        size = exe_file.stat().st_size / (1024*1024)
        print(f"   📦 文件大小: {size:.1f} MB")
        
        # 尝试使用PowerShell检查图标资源
        try:
            ps_command = f'''
Add-Type -AssemblyName System.Drawing
$icon = [System.Drawing.Icon]::ExtractAssociatedIcon("{exe_file}")
if ($icon) {{
    Write-Output "图标资源存在: $($icon.Width)x$($icon.Height)"
}} else {{
    Write-Output "图标资源不存在"
}}
'''
            result = subprocess.run(
                ["powershell", "-Command", ps_command],
                capture_output=True, text=True, timeout=10
            )
            
            if result.returncode == 0 and result.stdout.strip():
                print(f"   ✅ 图标检查: {result.stdout.strip()}")
            else:
                print(f"   ⚠️ 图标检查失败: {result.stderr}")
                
        except Exception as e:
            print(f"   ⚠️ 无法检查图标资源: {e}")
    
    return True

def diagnose_code_paths():
    """诊断代码中的图片加载路径"""
    print("\n🔍 第四步：诊断代码中的图片加载路径")
    print("=" * 50)
    
    # 检查主窗口图标设置
    main_window_file = Path("gui/main_window.py")
    if main_window_file.exists():
        print("📄 检查 gui/main_window.py 中的图标设置...")
        with open(main_window_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
        if "set_window_icon" in content:
            print("   ✅ 找到窗口图标设置函数")
            # 提取图标路径逻辑
            if "2.ico" in content:
                print("   ✅ 代码中引用了2.ico文件")
            else:
                print("   ❌ 代码中未找到2.ico引用")
        else:
            print("   ❌ 未找到窗口图标设置函数")
    else:
        print("❌ gui/main_window.py 文件不存在")
    
    # 检查帮助窗口图片加载
    help_window_file = Path("gui/help_window.py")
    if help_window_file.exists():
        print("\n📄 检查 gui/help_window.py 中的图片加载...")
        with open(help_window_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
        if "12.jpg" in content:
            print("   ✅ 代码中引用了12.jpg文件")
            # 检查路径构建逻辑
            if "os.path.join" in content and "os.path.dirname" in content:
                print("   ✅ 使用了相对路径构建")
            else:
                print("   ⚠️ 路径构建可能有问题")
        else:
            print("   ❌ 代码中未找到12.jpg引用")
    else:
        print("❌ gui/help_window.py 文件不存在")

def create_test_program():
    """创建测试程序来验证图标和图片加载"""
    print("\n🔧 第五步：创建图标图片测试程序")
    print("=" * 50)
    
    test_program = '''#!/usr/bin/env python3
# encoding:utf-8
"""
图标和图片显示测试程序
"""

import os
import sys
from pathlib import Path
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QPushButton
from PyQt5.QtGui import QPixmap, QIcon
from PyQt5.QtCore import Qt

class IconTestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("图标和图片显示测试")
        self.setGeometry(100, 100, 600, 500)
        
        # 设置窗口图标
        self.setup_window_icon()
        
        # 创建界面
        self.setup_ui()
    
    def setup_window_icon(self):
        """设置窗口图标"""
        print("🔧 设置窗口图标...")
        
        # 获取当前目录
        if hasattr(sys, '_MEIPASS'):
            # PyInstaller打包后的临时目录
            current_dir = Path(sys._MEIPASS)
            print(f"   PyInstaller模式，临时目录: {current_dir}")
        else:
            # 开发模式
            current_dir = Path(__file__).parent
            print(f"   开发模式，当前目录: {current_dir}")
        
        # 尝试多个可能的图标路径
        icon_paths = [
            current_dir / "2.ico",
            Path(sys.executable).parent / "2.ico",
            Path.cwd() / "2.ico"
        ]
        
        icon_set = False
        for icon_path in icon_paths:
            print(f"   尝试图标路径: {icon_path}")
            if icon_path.exists():
                try:
                    icon = QIcon(str(icon_path))
                    if not icon.isNull():
                        self.setWindowIcon(icon)
                        print(f"   ✅ 窗口图标设置成功: {icon_path}")
                        icon_set = True
                        break
                except Exception as e:
                    print(f"   ❌ 图标加载失败: {e}")
            else:
                print(f"   ❌ 图标文件不存在: {icon_path}")
        
        if not icon_set:
            print("   ⚠️ 使用默认图标")
    
    def setup_ui(self):
        """设置用户界面"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 标题
        title_label = QLabel("图标和图片显示测试")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("font-size: 18px; font-weight: bold; margin: 10px;")
        layout.addWidget(title_label)
        
        # 测试12.jpg
        self.test_image_display(layout, "12.jpg", "技术支持联系方式图片")
        
        # 测试33.png
        self.test_image_display(layout, "33.png", "捐赠二维码图片")
        
        # 路径信息
        info_label = QLabel()
        info_text = f"""
当前工作目录: {Path.cwd()}
程序执行路径: {Path(sys.executable).parent}
脚本文件路径: {Path(__file__).parent if '__file__' in globals() else '未知'}
"""
        if hasattr(sys, '_MEIPASS'):
            info_text += f"PyInstaller临时目录: {sys._MEIPASS}\\n"
        
        info_label.setText(info_text)
        info_label.setStyleSheet("font-size: 10px; color: gray; margin: 10px;")
        layout.addWidget(info_label)
        
        # 关闭按钮
        close_btn = QPushButton("关闭")
        close_btn.clicked.connect(self.close)
        layout.addWidget(close_btn)
    
    def test_image_display(self, layout, filename, description):
        """测试图片显示"""
        print(f"🖼️ 测试图片: {filename}")
        
        # 创建图片标签
        image_label = QLabel()
        image_label.setAlignment(Qt.AlignCenter)
        image_label.setStyleSheet("border: 1px solid #ccc; margin: 5px; padding: 10px; min-height: 150px;")
        
        # 获取当前目录
        if hasattr(sys, '_MEIPASS'):
            current_dir = Path(sys._MEIPASS)
        else:
            current_dir = Path(__file__).parent
        
        # 尝试多个可能的图片路径
        image_paths = [
            current_dir / filename,
            Path(sys.executable).parent / filename,
            Path.cwd() / filename
        ]
        
        image_loaded = False
        for image_path in image_paths:
            print(f"   尝试图片路径: {image_path}")
            if image_path.exists():
                try:
                    pixmap = QPixmap(str(image_path))
                    if not pixmap.isNull():
                        # 缩放图片
                        scaled_pixmap = pixmap.scaled(200, 150, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                        image_label.setPixmap(scaled_pixmap)
                        image_label.setText(f"✅ {description}\\n({filename})")
                        print(f"   ✅ 图片加载成功: {image_path}")
                        image_loaded = True
                        break
                except Exception as e:
                    print(f"   ❌ 图片加载失败: {e}")
            else:
                print(f"   ❌ 图片文件不存在: {image_path}")
        
        if not image_loaded:
            image_label.setText(f"❌ {description}\\n({filename} 未找到)")
            print(f"   ❌ {filename} 加载失败")
        
        layout.addWidget(image_label)

def main():
    """主函数"""
    print("🧪 图标和图片显示测试程序启动")
    print("=" * 40)
    
    app = QApplication(sys.argv)
    window = IconTestWindow()
    window.show()
    
    print("✅ 测试窗口已显示")
    print("💡 请检查:")
    print("   1. 窗口标题栏是否显示正确图标")
    print("   2. 任务栏中的程序图标是否正确")
    print("   3. 窗口中的图片是否正确显示")
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
'''
    
    # 保存测试程序
    with open("icon_image_test.py", 'w', encoding='utf-8') as f:
        f.write(test_program)
    
    print("✅ 测试程序创建完成: icon_image_test.py")
    
    # 为每个dist目录创建测试程序副本
    dist_base = Path("dist")
    if dist_base.exists():
        for dist_dir in dist_base.iterdir():
            if dist_dir.is_dir():
                test_copy = dist_dir / "icon_image_test.py"
                shutil.copy2("icon_image_test.py", test_copy)
                print(f"   📋 复制到: {test_copy}")

def main():
    """主函数"""
    print("🔍 企业微信自动回复系统 - 图标图片显示问题全面诊断")
    print("=" * 70)
    
    # 执行诊断步骤
    step1_ok = diagnose_source_files()
    step2_ok = diagnose_dist_directories()
    step3_ok = diagnose_icon_embedding()
    diagnose_code_paths()
    create_test_program()
    
    print("\n" + "=" * 70)
    print("📋 诊断结果总结")
    print("=" * 70)
    
    if step1_ok:
        print("✅ 源文件检查: 通过")
    else:
        print("❌ 源文件检查: 发现问题")
    
    if step2_ok:
        print("✅ Dist目录检查: 通过")
    else:
        print("❌ Dist目录检查: 发现问题")
    
    if step3_ok:
        print("✅ 图标嵌入检查: 完成")
    else:
        print("❌ 图标嵌入检查: 发现问题")
    
    print("\n💡 下一步建议:")
    print("1. 运行测试程序: python icon_image_test.py")
    print("2. 或在dist目录中运行: python icon_image_test.py")
    print("3. 检查测试程序的输出和显示效果")
    print("4. 根据测试结果确定具体的修复方案")

if __name__ == "__main__":
    main()
