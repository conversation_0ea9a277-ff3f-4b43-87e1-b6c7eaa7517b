#!/usr/bin/env python3
# encoding:utf-8
"""
运行时图标修复脚本
"""

import os
import sys
from pathlib import Path
from PyQt5.QtGui import QIcon, QPixmap
from PyQt5.QtCore import QSize

def fix_window_icon(window):
    """修复窗口图标"""
    try:
        # 获取当前目录
        current_dir = Path(__file__).parent if hasattr(__file__, '__file__') else Path.cwd()
        
        # 尝试多个可能的图标路径
        icon_paths = [
            current_dir / "2.ico",
            current_dir / "icon.ico",
            Path(sys.executable).parent / "2.ico",
            Path(os.getcwd()) / "2.ico"
        ]
        
        for icon_path in icon_paths:
            if icon_path.exists():
                try:
                    icon = QIcon(str(icon_path))
                    if not icon.isNull():
                        window.setWindowIcon(icon)
                        print(f"✅ 窗口图标设置成功: {icon_path}")
                        return True
                except Exception as e:
                    print(f"⚠️ 图标加载失败 {icon_path}: {e}")
                    continue
        
        # 如果所有路径都失败，创建默认图标
        print("🎨 创建默认图标...")
        pixmap = QPixmap(32, 32)
        pixmap.fill()
        
        from PyQt5.QtGui import QPainter, QBrush, QColor
        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # 绘制一个简单的图标
        painter.setBrush(QBrush(QColor(0, 120, 215)))  # 蓝色
        painter.drawEllipse(4, 4, 24, 24)
        
        # 绘制字母"W"
        painter.setPen(QColor(255, 255, 255))
        painter.drawText(12, 20, "W")
        
        painter.end()
        
        icon = QIcon(pixmap)
        window.setWindowIcon(icon)
        print("✅ 默认图标设置成功")
        return True
        
    except Exception as e:
        print(f"❌ 图标修复失败: {e}")
        return False

def fix_image_loading(image_label, image_filename, description):
    """修复图片加载"""
    try:
        # 获取当前目录
        current_dir = Path(__file__).parent if hasattr(__file__, '__file__') else Path.cwd()
        
        # 尝试多个可能的图片路径
        image_paths = [
            current_dir / image_filename,
            Path(sys.executable).parent / image_filename,
            Path(os.getcwd()) / image_filename
        ]
        
        for image_path in image_paths:
            if image_path.exists():
                try:
                    pixmap = QPixmap(str(image_path))
                    if not pixmap.isNull():
                        # 缩放图片到合适大小
                        if image_filename.endswith('.jpg'):
                            scaled_pixmap = pixmap.scaled(300, 300, 1, 1)  # Qt.KeepAspectRatio, Qt.SmoothTransformation
                        else:
                            scaled_pixmap = pixmap.scaled(280, 280, 1, 1)
                        
                        image_label.setPixmap(scaled_pixmap)
                        print(f"✅ {description}加载成功: {image_path}")
                        return True
                except Exception as e:
                    print(f"⚠️ 图片加载失败 {image_path}: {e}")
                    continue
        
        # 如果所有路径都失败，显示提示文字
        image_label.setText(f"📱 {description}\n({image_filename} 文件未找到)")
        print(f"❌ {description}未找到: {image_filename}")
        return False
        
    except Exception as e:
        print(f"❌ 图片修复失败: {e}")
        image_label.setText(f"📱 {description}\n(加载失败: {str(e)})")
        return False

# 导出修复函数
__all__ = ['fix_window_icon', 'fix_image_loading']
