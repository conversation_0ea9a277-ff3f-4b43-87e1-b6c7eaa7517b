
This file lists modules PyInstaller was not able to find. This does not
necessarily mean this module is required for running your program. Python and
Python 3rd-party packages include a lot of conditional or optional modules. For
example the module 'ntpath' only exists on Windows, whereas the module
'posixpath' only exists on Posix systems.

Types if import:
* top-level: imported at the top-level - look at these first
* conditional: imported within an if-statement
* delayed: imported within a function
* optional: imported within a try-except-statement

IMPORTANT: Do NOT post this list to the issue-tracker. Use it as a basis for
            tracking down the missing module yourself. Thanks!

missing module named pyimod02_importers - imported by E:\Desktop\企业微信\pack_env\Lib\site-packages\PyInstaller\hooks\rthooks\pyi_rth_pkgutil.py (top-level), E:\Desktop\企业微信\pack_env\Lib\site-packages\PyInstaller\hooks\rthooks\pyi_rth_pkgres.py (top-level)
missing module named pwd - imported by posixpath (delayed, conditional), subprocess (delayed, conditional, optional), shutil (delayed, optional), tarfile (optional), pathlib (delayed, optional), netrc (delayed, conditional), getpass (delayed), http.server (delayed, optional), webbrowser (delayed), distutils.util (delayed, conditional, optional), distutils.archive_util (optional), pip._vendor.distlib._backport.tarfile (optional), pip._vendor.distlib._backport.shutil (optional)
missing module named grp - imported by subprocess (delayed, conditional, optional), shutil (delayed, optional), tarfile (optional), pathlib (delayed, optional), distutils.archive_util (optional), pip._vendor.distlib._backport.tarfile (optional), pip._vendor.distlib._backport.shutil (optional)
missing module named _posixsubprocess - imported by subprocess (optional), multiprocessing.util (delayed)
missing module named fcntl - imported by subprocess (optional)
missing module named _manylinux - imported by pip._vendor.packaging._manylinux (delayed, optional), pkg_resources._vendor.packaging.tags (delayed, optional), packaging._manylinux (delayed, optional)
missing module named termios - imported by getpass (optional), tty (top-level)
missing module named pep517 - imported by importlib.metadata (delayed)
missing module named _frozen_importlib_external - imported by importlib._bootstrap (delayed), importlib (optional), importlib.abc (optional), zipimport (top-level), pip._vendor.distlib.resources (optional)
excluded module named _frozen_importlib - imported by importlib (optional), importlib.abc (optional), zipimport (top-level), pip._vendor.distlib.resources (optional)
missing module named posix - imported by os (conditional, optional), shutil (conditional), importlib._bootstrap_external (conditional)
missing module named resource - imported by posix (top-level)
missing module named readline - imported by cmd (delayed, conditional, optional), code (delayed, conditional, optional), pdb (delayed, optional), site (delayed, optional), rlcompleter (optional), websockets.cli (delayed, optional)
missing module named __builtin__ - imported by pip._vendor.pyparsing (conditional), pip._vendor.distlib.compat (conditional), pip._vendor.distlib._backport.tarfile (conditional), pkg_resources._vendor.pyparsing (conditional)
missing module named ordereddict - imported by pip._vendor.pyparsing (optional), pkg_resources._vendor.pyparsing (optional)
missing module named 'pkg_resources.extern.pyparsing' - imported by pkg_resources._vendor.packaging.markers (top-level), pkg_resources._vendor.packaging.requirements (top-level)
missing module named 'com.sun' - imported by pip._vendor.appdirs (delayed, conditional, optional), pkg_resources._vendor.appdirs (delayed, conditional, optional)
missing module named 'win32com.gen_py' - imported by win32com (conditional, optional), E:\Desktop\企业微信\pack_env\Lib\site-packages\PyInstaller\hooks\rthooks\pyi_rth_win32comgenpy.py (top-level)
missing module named _winreg - imported by platform (delayed, optional), pip._vendor.appdirs (delayed, conditional), pip._vendor.requests.utils (delayed, conditional, optional), pkg_resources._vendor.appdirs (delayed, conditional)
missing module named pkg_resources.extern.packaging - imported by pkg_resources.extern (top-level), pkg_resources (top-level)
missing module named pkg_resources.extern.appdirs - imported by pkg_resources.extern (top-level), pkg_resources (top-level)
missing module named 'org.python' - imported by copy (optional), xml.sax (delayed, conditional)
missing module named 'java.lang' - imported by platform (delayed, optional), xml.sax._exceptions (conditional)
missing module named _posixshmem - imported by multiprocessing.resource_tracker (conditional), multiprocessing.shared_memory (conditional)
missing module named multiprocessing.BufferTooShort - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named multiprocessing.AuthenticationError - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named multiprocessing.get_context - imported by multiprocessing (top-level), multiprocessing.pool (top-level), multiprocessing.managers (top-level), multiprocessing.sharedctypes (top-level)
missing module named multiprocessing.TimeoutError - imported by multiprocessing (top-level), multiprocessing.pool (top-level)
missing module named multiprocessing.set_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named multiprocessing.get_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named vms_lib - imported by platform (delayed, optional)
missing module named java - imported by platform (delayed), pip._vendor.distlib.scripts (delayed, conditional)
missing module named usercustomize - imported by site (delayed, optional)
missing module named sitecustomize - imported by site (delayed, optional)
missing module named parser - imported by E:\Desktop\企业微信\gui_app.py (top-level)
missing module named org - imported by pickle (optional)
missing module named annotationlib - imported by typing_extensions (conditional), attr._compat (conditional)
missing module named asyncio.timeout_at - imported by asyncio (conditional), websockets.asyncio.compatibility (conditional)
missing module named asyncio.timeout - imported by asyncio (conditional), websockets.asyncio.compatibility (conditional)
missing module named asyncio.DefaultEventLoopPolicy - imported by asyncio (delayed, conditional), asyncio.events (delayed, conditional)
missing module named simplejson - imported by requests.compat (conditional, optional)
missing module named 'OpenSSL.crypto' - imported by urllib3.contrib.pyopenssl (delayed, conditional), pip._vendor.urllib3.contrib.pyopenssl (delayed)
missing module named 'cryptography.x509' - imported by urllib3.contrib.pyopenssl (delayed, optional), pip._vendor.urllib3.contrib.pyopenssl (delayed, optional)
missing module named cryptography - imported by urllib3.contrib.pyopenssl (top-level), requests (conditional, optional), pip._vendor.urllib3.contrib.pyopenssl (top-level), pip._vendor.requests (conditional, optional)
missing module named OpenSSL - imported by urllib3.contrib.pyopenssl (top-level)
missing module named 'pyodide.ffi' - imported by urllib3.contrib.emscripten.fetch (delayed, optional)
missing module named pyodide - imported by urllib3.contrib.emscripten.fetch (top-level)
missing module named js - imported by urllib3.contrib.emscripten.fetch (top-level)
missing module named 'typing.io' - imported by importlib.resources (top-level)
missing module named 'h2.events' - imported by urllib3.http2.connection (top-level)
missing module named 'h2.connection' - imported by urllib3.http2.connection (top-level)
missing module named h2 - imported by urllib3.http2.connection (top-level)
missing module named zstandard - imported by urllib3.util.request (optional), urllib3.response (optional)
missing module named compression - imported by urllib3.util.request (optional), urllib3.response (optional)
missing module named brotli - imported by urllib3.util.request (optional), urllib3.response (optional), pip._vendor.urllib3.util.request (optional), pip._vendor.urllib3.response (optional), aiohttp.compression_utils (optional)
missing module named brotlicffi - imported by urllib3.util.request (optional), urllib3.response (optional), aiohttp.compression_utils (optional)
missing module named dummy_threading - imported by requests.cookies (optional), pip._vendor.requests.cookies (optional), pip._internal.utils.logging (optional), pip._vendor.distlib.util (optional)
missing module named socks - imported by urllib3.contrib.socks (optional), pip._vendor.urllib3.contrib.socks (optional)
missing module named chardet - imported by requests (optional)
missing module named dulwich - imported by common.package_manager (delayed, optional), plugins.plugin_manager (delayed)
missing module named com - imported by pip._vendor.appdirs (delayed)
missing module named 'pip._vendor.six.moves' - imported by pip._vendor.pkg_resources (top-level), pip._vendor.html5lib._inputstream (top-level), pip._vendor.html5lib.filters.sanitizer (top-level)
missing module named lxml - imported by pip._vendor.html5lib.treebuilders.etree_lxml (top-level), pip._vendor.html5lib.treewalkers.etree_lxml (top-level)
missing module named 'genshi.core' - imported by pip._vendor.html5lib.treewalkers.genshi (top-level)
missing module named genshi - imported by pip._vendor.html5lib.treewalkers.genshi (top-level)
missing module named _cmsgpack - imported by pip._vendor.msgpack (conditional, optional)
missing module named '__pypy__.builders' - imported by pip._vendor.msgpack.fallback (conditional, optional)
missing module named __pypy__ - imported by pip._vendor.msgpack.fallback (conditional)
missing module named StringIO - imported by pip._vendor.urllib3.packages.six (conditional), pip._vendor.six (conditional), pip._vendor.requests.compat (conditional), pip._vendor.distlib.compat (conditional)
missing module named wheel - imported by pip._internal.utils.misc (delayed, optional)
missing module named backports - imported by pip._vendor.urllib3.packages.ssl_match_hostname (optional)
missing module named 'pip._vendor.urllib3.packages.six.moves' - imported by pip._vendor.urllib3.exceptions (top-level), pip._vendor.urllib3.connection (top-level), pip._vendor.urllib3.util.response (top-level), pip._vendor.urllib3.connectionpool (top-level), pip._vendor.urllib3.request (top-level), pip._vendor.urllib3.util.queue (top-level), pip._vendor.urllib3.poolmanager (top-level)
missing module named Queue - imported by pip._vendor.urllib3.util.queue (conditional), pip._vendor.distlib.compat (conditional)
missing module named 'cryptography.hazmat' - imported by pip._vendor.urllib3.contrib.pyopenssl (top-level)
missing module named 'OpenSSL.SSL' - imported by pip._vendor.urllib3.contrib.pyopenssl (top-level)
missing module named 'tornado.concurrent' - imported by pip._vendor.tenacity.tornadoweb (conditional)
missing module named tornado - imported by pip._vendor.tenacity.tornadoweb (top-level)
missing module named __main__ - imported by pip._vendor.pkg_resources (delayed, optional)
missing module named Cookie - imported by pip._vendor.requests.compat (conditional)
missing module named cookielib - imported by pip._vendor.requests.compat (conditional)
missing module named urllib2 - imported by pip._vendor.requests.compat (conditional), pip._vendor.distlib.compat (conditional)
missing module named urlparse - imported by pip._vendor.requests.compat (conditional), pip._vendor.cachecontrol.compat (optional), pip._vendor.distlib.compat (conditional)
missing module named 'lockfile.mkdirlockfile' - imported by pip._vendor.cachecontrol.caches.file_cache (delayed, optional)
missing module named lockfile - imported by pip._vendor.cachecontrol.caches.file_cache (delayed, optional)
missing module named 'pip._vendor.requests.packages.urllib3' - imported by pip._vendor.cachecontrol.compat (optional)
missing module named cPickle - imported by pip._vendor.cachecontrol.compat (optional)
missing module named cStringIO - imported by cPickle (top-level)
missing module named copy_reg - imported by cPickle (top-level), cStringIO (top-level)
missing module named keyring - imported by pip._internal.network.auth (optional)
missing module named _abcoll - imported by pip._vendor.distlib.compat (optional)
missing module named dummy_thread - imported by pip._vendor.distlib.compat (optional)
missing module named thread - imported by pip._vendor.distlib.compat (optional)
missing module named htmlentitydefs - imported by pip._vendor.distlib.compat (conditional)
missing module named HTMLParser - imported by pip._vendor.distlib.compat (conditional)
missing module named xmlrpclib - imported by pip._vendor.distlib.compat (conditional)
missing module named httplib - imported by pip._vendor.distlib.compat (conditional)
missing module named ConfigParser - imported by pip._vendor.distlib.compat (conditional), pip._vendor.distlib._backport.sysconfig (optional)
missing module named pip.__file__ - imported by pip (top-level), pip._internal.build_env (top-level)
missing module named toml - imported by pip._vendor.pep517.compat (conditional)
missing module named 'voice.audio_convert' - imported by channel.chat_channel (optional), channel.wechat.wechaty_channel (optional), channel.wechatmp.wechatmp_channel (top-level), channel.wechatcom.wechatcomapp_channel (top-level), channel.wechatcs.wechatcomservice_channel (top-level), channel.gewechat.gewechat_channel (top-level)
missing module named 'lib.gewechat' - imported by channel.gewechat.gewechat_message (top-level), channel.gewechat.gewechat_channel (top-level)
missing module named web - imported by channel.web.web_channel (top-level), channel.wechatmp.wechatmp_channel (top-level), channel.wechatmp.common (top-level), channel.wechatcom.wechatcomapp_channel (top-level), channel.wechatcs.wechatcomservice_channel (top-level), channel.feishu.feishu_channel (top-level), channel.gewechat.gewechat_channel (top-level)
missing module named 'dingtalk_stream.card_replier' - imported by channel.dingtalk.dingtalk_channel (top-level)
missing module named dingtalk_stream - imported by channel.dingtalk.dingtalk_channel (top-level), channel.dingtalk.dingtalk_message (top-level)
missing module named 'wechatpy.exceptions' - imported by channel.wechatmp.wechatmp_channel (top-level), channel.wechatmp.common (top-level), channel.wechatmp.wechatmp_client (top-level), channel.wechatcom.wechatcomapp_channel (top-level), channel.wechatcs.wechatcomservice_channel (top-level)
missing module named 'wechatpy.enterprise' - imported by channel.wechatcom.wechatcomapp_channel (top-level), channel.wechatcom.wechatcomapp_client (top-level), channel.wechatcom.wechatcomapp_message (top-level), channel.wechatcs.wechatcomservice_channel (top-level), channel.wechatcs.wechatcomservice_message (top-level)
missing module named 'wechatpy.client' - imported by channel.wechatmp.wechatmp_client (top-level)
missing module named 'wechatpy.utils' - imported by channel.wechatmp.common (top-level)
missing module named 'wechatpy.crypto' - imported by channel.wechatmp.common (top-level)
missing module named wechatpy - imported by channel.wechatmp.wechatmp_channel (top-level)
missing module named wcferry - imported by channel.wechat.wcf_message (top-level), channel.wechat.wcf_channel (top-level)
missing module named wechaty_puppet - imported by channel.wechat.wechaty_channel (top-level)
missing module named 'wechaty.user' - imported by channel.wechat.wechaty_channel (top-level), channel.wechat.wechaty_message (top-level)
missing module named wechaty - imported by channel.wechat.wechaty_channel (top-level), channel.wechat.wechaty_message (top-level)
missing module named linkai - imported by bot.linkai.link_ai_bot (delayed, optional), common.linkai_client (top-level)
missing module named qrcode - imported by channel.wechat.wechat_channel (delayed, conditional)
missing module named 'lib.itchat.content' - imported by channel.wechat.wechat_message (top-level), channel.wechat.wechat_channel (top-level)
missing module named lib.itchat - imported by lib (top-level), channel.wechat.wechat_message (top-level), channel.wechat.wechat_channel (top-level)
missing module named 'openai.error' - imported by bot.chatgpt.chat_gpt_bot (top-level), bot.openai.open_ai_image (top-level), bot.openai.open_ai_bot (top-level), bot.claudeapi.claude_api_bot (top-level), bot.ali.ali_qwen_bot (top-level), bot.zhipuai.zhipuai_bot (top-level), bot.moonshot.moonshot_bot (top-level), bot.minimax.minimax_bot (top-level), bot.modelscope.modelscope_bot (top-level)
missing module named openai - imported by bot.chatgpt.chat_gpt_bot (top-level), bot.openai.open_ai_image (top-level), bot.openai.open_ai_bot (top-level), bot.claudeapi.claude_api_bot (top-level), bot.ali.ali_qwen_bot (top-level), bot.zhipuai.zhipuai_bot (top-level), bot.moonshot.moonshot_bot (top-level), bot.minimax.minimax_bot (top-level), bot.deepseek.deepseek_bot (top-level), bot.modelscope.modelscope_bot (top-level)
missing module named tiktoken - imported by bot.chatgpt.chat_gpt_session (delayed), bot.openai.open_ai_session (delayed)
missing module named cozepy - imported by bot.bytedance.coze_client (top-level), bot.bytedance.bytedance_coze_bot (top-level)
missing module named zhipuai - imported by bot.zhipuai.zhipu_ai_image (delayed), bot.zhipuai.zhipuai_bot (top-level)
missing module named 'lib.dify' - imported by bot.dify.dify_bot (top-level)
missing module named 'google.generativeai' - imported by bot.gemini.google_gemini_bot (top-level)
missing module named google - imported by bot.gemini.google_gemini_bot (top-level)
missing module named dashscope - imported by bot.dashscope.dashscope_bot (top-level)
missing module named broadscope_bailian - imported by bot.ali.ali_qwen_bot (top-level)
missing module named anthropic - imported by bot.claudeapi.claude_api_bot (top-level)
missing module named curl_cffi - imported by bot.claude.claude_ai_bot (top-level)
missing module named websocket - imported by bot.xunfei.xunfei_spark_bot (top-level)
missing module named aiodns - imported by aiohttp.resolver (optional)
missing module named uvloop - imported by aiohttp.worker (delayed)
missing module named 'gunicorn.workers' - imported by aiohttp.worker (top-level)
missing module named gunicorn - imported by aiohttp.worker (top-level)
missing module named 'werkzeug.routing' - imported by websockets.asyncio.router (top-level), websockets.sync.router (top-level)
missing module named 'werkzeug.exceptions' - imported by websockets.sync.router (top-level)
missing module named 'python_socks.sync' - imported by websockets.sync.client (optional)
missing module named python_socks - imported by websockets.asyncio.client (optional), websockets.sync.client (optional)
missing module named werkzeug - imported by websockets.asyncio.router (top-level)
missing module named 'python_socks.async_' - imported by websockets.asyncio.client (optional)
missing module named trio - imported by pyee.trio (top-level)
missing module named 'twisted.python' - imported by pyee.twisted (top-level)
missing module named twisted - imported by pyee.twisted (top-level)
missing module named _dummy_thread - imported by numpy._core.arrayprint (optional)
missing module named 'numpy_distutils.cpuinfo' - imported by numpy.f2py.diagnose (delayed, conditional, optional)
missing module named 'numpy_distutils.fcompiler' - imported by numpy.f2py.diagnose (delayed, conditional, optional)
missing module named 'numpy_distutils.command' - imported by numpy.f2py.diagnose (delayed, conditional, optional)
missing module named numpy_distutils - imported by numpy.f2py.diagnose (delayed, optional)
missing module named psutil - imported by numpy.testing._private.utils (delayed, optional)
missing module named threadpoolctl - imported by numpy.lib._utils_impl (delayed, optional)
missing module named numpy._core.zeros - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.vstack - imported by numpy._core (top-level), numpy.lib._shape_base_impl (top-level), numpy (conditional)
missing module named numpy._core.void - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.vecmat - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.vecdot - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.ushort - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.unsignedinteger - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.ulonglong - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.ulong - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.uintp - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.uintc - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.uint64 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.uint32 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.uint16 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.uint - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.ubyte - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.trunc - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.true_divide - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.transpose - imported by numpy._core (top-level), numpy.lib._function_base_impl (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.trace - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.timedelta64 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.tensordot - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.tanh - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.tan - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.swapaxes - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.sum - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.subtract - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.str_ - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.square - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.sqrt - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional), numpy.fft._pocketfft (top-level)
missing module named numpy._core.spacing - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.sort - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.sinh - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.single - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.signedinteger - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.signbit - imported by numpy._core (delayed), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.sign - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.short - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.rint - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.right_shift - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.result_type - imported by numpy._core (delayed), numpy.testing._private.utils (delayed), numpy (conditional), numpy.fft._pocketfft (top-level)
missing module named numpy._core.remainder - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.reciprocal - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional), numpy.fft._pocketfft (top-level)
missing module named numpy._core.radians - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.rad2deg - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.prod - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.power - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.positive - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.pi - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.outer - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.ones - imported by numpy._core (top-level), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.object_ - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.number - imported by numpy._core (delayed), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.not_equal - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.newaxis - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.negative - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.ndarray - imported by numpy._core (top-level), numpy.lib._utils_impl (top-level), numpy.testing._private.utils (top-level), numpy (conditional)
missing module named numpy._core.multiply - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.moveaxis - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.modf - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.mod - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.minimum - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.maximum - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.max - imported by numpy._core (delayed), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.matrix_transpose - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.matvec - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.matmul - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.longdouble - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.long - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.logical_xor - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.logical_or - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.logical_not - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.logical_and - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.logaddexp2 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.logaddexp - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.log2 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.log1p - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.log - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.linspace - imported by numpy._core (top-level), numpy.lib._index_tricks_impl (top-level), numpy (conditional)
missing module named numpy._core.less_equal - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.less - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.left_shift - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.ldexp - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.lcm - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.isscalar - imported by numpy._core (delayed), numpy.testing._private.utils (delayed), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.isnat - imported by numpy._core (top-level), numpy.testing._private.utils (top-level), numpy (conditional)
missing module named numpy._core.isnan - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.isfinite - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.intp - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (top-level), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.integer - imported by numpy._core (conditional), numpy (conditional), numpy.fft._helper (top-level)
missing module named numpy._core.intc - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.int8 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.int64 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.int32 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.int16 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.inf - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.inexact - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.iinfo - imported by numpy._core (top-level), numpy.lib._twodim_base_impl (top-level), numpy (conditional)
missing module named numpy._core.hypot - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.hstack - imported by numpy._core (top-level), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.heaviside - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.half - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.greater_equal - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.greater - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.gcd - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.frompyfunc - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.frexp - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.fmod - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.fmin - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.fmax - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.floor_divide - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.floor - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.floating - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.float_power - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.float32 - imported by numpy._core (top-level), numpy.testing._private.utils (top-level), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.float16 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.finfo - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.fabs - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.expm1 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.exp - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.euler_gamma - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.errstate - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.equal - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.empty_like - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional), numpy.fft._pocketfft (top-level)
missing module named numpy._core.empty - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (top-level), numpy (conditional), numpy.fft._helper (top-level)
missing module named numpy._core.e - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.double - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.dot - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.divmod - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.divide - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.diagonal - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.degrees - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.deg2rad - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.datetime64 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.csingle - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.cross - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.count_nonzero - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.cosh - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.cos - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.copysign - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.conjugate - imported by numpy._core (conditional), numpy (conditional), numpy.fft._pocketfft (top-level)
missing module named numpy._core.conj - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.complexfloating - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.complex64 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.clongdouble - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.character - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.ceil - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.cdouble - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.cbrt - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.bytes_ - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.byte - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.bool_ - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.bitwise_xor - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.bitwise_or - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.bitwise_count - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.bitwise_and - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.atleast_3d - imported by numpy._core (top-level), numpy.lib._shape_base_impl (top-level), numpy (conditional)
missing module named numpy._core.atleast_2d - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.atleast_1d - imported by numpy._core (top-level), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.asarray - imported by numpy._core (top-level), numpy.lib._array_utils_impl (top-level), numpy.linalg._linalg (top-level), numpy (conditional), numpy.fft._pocketfft (top-level), numpy.fft._helper (top-level)
missing module named numpy._core.asanyarray - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.array_repr - imported by numpy._core (top-level), numpy.testing._private.utils (top-level), numpy (conditional)
missing module named numpy._core.array2string - imported by numpy._core (delayed), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.array - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (top-level), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.argsort - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.arctanh - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arctan2 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arctan - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arcsinh - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arcsin - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arccosh - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arccos - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arange - imported by numpy._core (top-level), numpy.testing._private.utils (top-level), numpy (conditional), numpy.fft._helper (top-level)
missing module named numpy._core.amin - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.amax - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.all - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.add - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named yaml - imported by numpy.__config__ (delayed)
missing module named numpy._distributor_init_local - imported by numpy (optional), numpy._distributor_init (optional)
missing module named olefile - imported by PIL.FpxImagePlugin (top-level), PIL.MicImagePlugin (top-level)
missing module named defusedxml - imported by PIL.Image (optional)
missing module named _scproxy - imported by urllib.request (conditional)
