#!/usr/bin/env python3
# encoding:utf-8
"""
创建带控制台输出的调试版本
"""

import os
import sys
import shutil
from pathlib import Path

def create_debug_console_spec():
    """创建带控制台输出的调试版spec文件"""
    print("📝 创建带控制台输出的调试版spec文件...")
    
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-
# 调试版本 - 带控制台输出

import os
from pathlib import Path

project_root = os.path.dirname(os.path.abspath(SPEC))

# 数据文件 - 只包含必要的配置文件
datas = []

# 配置文件
config_files = ['config-template.json', 'config-gui.json']
for config_file in config_files:
    config_path = os.path.join(project_root, config_file)
    if os.path.exists(config_path):
        datas.append((config_path, '.'))

# ntwork目录
ntwork_path = os.path.join(project_root, 'ntwork')
if os.path.exists(ntwork_path):
    for root, dirs, files in os.walk(ntwork_path):
        for file in files:
            if not file.endswith('.pyc') and '__pycache__' not in root:
                src_path = os.path.join(root, file)
                rel_path = os.path.relpath(src_path, project_root)
                dest_path = os.path.dirname(rel_path)
                datas.append((src_path, dest_path))

# 隐藏导入
hiddenimports = [
    'PyQt5.QtCore', 'PyQt5.QtGui', 'PyQt5.QtWidgets', 'PyQt5.sip', 'PyQt5.QtPrintSupport',
    'PIL', 'PIL.Image', 'PIL.ImageTk', 'PIL.ImageDraw', 'PIL.ImageFont', 'PIL.ImageFilter',
    'PIL.ImageEnhance', 'PIL.ImageOps', 'PIL.ImageChops', 'PIL.ImageStat', 'PIL.ImageColor',
    'PIL.ImageFile', 'PIL.ImageSequence', 'PIL.ImageShow', 'PIL.ImageWin', 'PIL.ImageGrab',
    'PIL.ImagePath', 'PIL.ImageQt', 'PIL.ImageCms', 'PIL.ImageMath', 'PIL.ImageMode',
    'PIL.ImagePalette', 'PIL.ImageTransform', 'PIL._imaging', 'PIL._imagingft',
    'PIL._imagingmath', 'PIL._imagingmorph', 'PIL._imagingcms', 'PIL._webp', 'PIL._tkinter_finder',
    'ntwork', 'ntwork.core', 'ntwork.core.wework', 'ntwork.core.mgr', 'ntwork.utils',
    'ntwork.utils.logger', 'ntwork.utils.singleton', 'ntwork.utils.xdg', 'ntwork.const',
    'ntwork.const.notify_type', 'ntwork.const.send_type', 'ntwork.exception', 'ntwork.wc',
    'pilk', 'pyee', 'pyee.base', 'pyee.asyncio', 'websockets', 'websockets.client',
    'websockets.server', 'aiohttp', 'aiohttp.client', 'aiohttp.web',
    'gui.main_window', 'gui.controllers', 'gui.config_loader', 'gui.managers',
    'gui.system_tray', 'gui.help_window', 'gui.donate_window', 'config',
    'common.log', 'common.utils', 'common.singleton', 'common.embedded_resources',
    'bridge.bridge', 'bridge.context', 'bridge.reply', 'bot.bot_factory',
    'channel.channel_factory', 'plugins.plugin_manager',
    'requests', 'json', 'threading', 'queue', 'sqlite3', 'configparser', 'logging',
    'ctypes', 'ctypes.wintypes', 'win32api', 'win32con', 'win32gui', 'pywintypes',
    'asyncio', 'typing_extensions', 'dataclasses', 'base64', 'tempfile',
]

excludes = ['matplotlib', 'scipy', 'tensorflow', 'torch', 'tkinter', 'pygame', 'voice']

block_cipher = None

a = Analysis(
    ['gui_app.py'],
    pathex=[project_root],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=excludes,
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

# 图标文件路径
icon_path = os.path.join(project_root, '2.ico')

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='企业微信自动回复系统_调试控制台版',
    debug=True,  # 启用调试
    bootloader_ignore_signals=False,
    strip=False,
    upx=False,
    console=True,  # 显示控制台
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=icon_path if os.path.exists(icon_path) else None,
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=False,
    upx_exclude=[],
    name='企业微信自动回复系统_调试控制台版',
)
'''
    
    with open('debug_console.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✅ 调试控制台版spec文件创建完成")

def create_simple_test_app():
    """创建简单的测试应用"""
    print("🧪 创建简单的测试应用...")
    
    test_app_code = '''#!/usr/bin/env python3
# encoding:utf-8
"""
简单的测试应用 - 验证基础功能
"""

import sys
import os
import traceback

def main():
    """主函数"""
    print("🧪 企业微信自动回复系统 - 简单测试")
    print("=" * 50)
    
    try:
        print("1. 测试Python基础环境...")
        print(f"   Python版本: {sys.version}")
        print(f"   当前目录: {os.getcwd()}")
        print(f"   可执行文件: {sys.executable}")
        print("   ✅ Python环境正常")
        
        print("\\n2. 测试PyQt5导入...")
        from PyQt5.QtWidgets import QApplication, QMainWindow, QLabel, QVBoxLayout, QWidget
        from PyQt5.QtCore import Qt
        from PyQt5.QtGui import QIcon
        print("   ✅ PyQt5导入成功")
        
        print("\\n3. 测试嵌入式资源...")
        try:
            from common.embedded_resources import get_icon_path, get_contact_image_path
            icon_path = get_icon_path()
            image_path = get_contact_image_path()
            print(f"   图标路径: {icon_path}")
            print(f"   图片路径: {image_path}")
            print("   ✅ 嵌入式资源正常")
        except Exception as e:
            print(f"   ❌ 嵌入式资源错误: {e}")
            traceback.print_exc()
        
        print("\\n4. 创建简单GUI窗口...")
        app = QApplication(sys.argv)
        
        window = QMainWindow()
        window.setWindowTitle("企业微信自动回复系统 - 测试窗口")
        window.setGeometry(100, 100, 400, 300)
        
        # 设置图标
        try:
            icon_path = get_icon_path()
            if icon_path and os.path.exists(icon_path):
                icon = QIcon(icon_path)
                window.setWindowIcon(icon)
                print("   ✅ 窗口图标设置成功")
            else:
                print("   ⚠️ 图标文件不存在，使用默认图标")
        except Exception as e:
            print(f"   ❌ 图标设置失败: {e}")
        
        # 创建内容
        central_widget = QWidget()
        window.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 标题
        title_label = QLabel("🎉 企业微信自动回复系统测试成功！")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #2c3e50; margin: 20px;")
        layout.addWidget(title_label)
        
        # 状态信息
        status_label = QLabel(f"""
✅ Python环境: {sys.version.split()[0]}
✅ PyQt5: 正常加载
✅ 嵌入式资源: 正常工作
✅ GUI界面: 正常显示
✅ 图标显示: {'成功' if icon_path and os.path.exists(icon_path) else '使用默认'}

🎯 如果您能看到这个窗口，说明基础环境完全正常！
""")
        status_label.setStyleSheet("font-size: 12px; color: #34495e; margin: 10px;")
        layout.addWidget(status_label)
        
        # 测试图片显示
        try:
            from PyQt5.QtGui import QPixmap
            image_path = get_contact_image_path()
            if image_path and os.path.exists(image_path):
                pixmap = QPixmap(image_path)
                if not pixmap.isNull():
                    image_label = QLabel()
                    scaled_pixmap = pixmap.scaled(200, 150, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                    image_label.setPixmap(scaled_pixmap)
                    image_label.setAlignment(Qt.AlignCenter)
                    layout.addWidget(image_label)
                    print("   ✅ 联系方式图片显示成功")
                else:
                    print("   ❌ 图片加载失败")
            else:
                print("   ❌ 图片文件不存在")
        except Exception as e:
            print(f"   ❌ 图片显示错误: {e}")
        
        print("\\n5. 显示测试窗口...")
        window.show()
        print("   ✅ 测试窗口已显示")
        
        print("\\n🎉 所有测试完成！窗口正在显示中...")
        print("💡 请检查:")
        print("   - 窗口是否正确显示")
        print("   - 窗口图标是否正确")
        print("   - 联系方式图片是否显示")
        
        # 运行应用
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"\\n❌ 测试失败: {e}")
        traceback.print_exc()
        input("\\n按回车键退出...")
        return 1

if __name__ == "__main__":
    main()
'''
    
    with open('simple_test_app.py', 'w', encoding='utf-8') as f:
        f.write(test_app_code)
    
    print("✅ 简单测试应用创建完成: simple_test_app.py")

def create_simple_test_spec():
    """创建简单测试应用的spec文件"""
    print("📝 创建简单测试应用的spec文件...")
    
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-
# 简单测试应用spec文件

import os
from pathlib import Path

project_root = os.path.dirname(os.path.abspath(SPEC))

# 最小化的数据文件
datas = []

# 隐藏导入 - 只包含必要的
hiddenimports = [
    'PyQt5.QtCore', 'PyQt5.QtGui', 'PyQt5.QtWidgets', 'PyQt5.sip',
    'common.embedded_resources',
    'base64', 'tempfile',
]

excludes = ['matplotlib', 'scipy', 'tensorflow', 'torch', 'tkinter', 'pygame', 'voice', 'ntwork', 'pilk']

block_cipher = None

a = Analysis(
    ['simple_test_app.py'],
    pathex=[project_root],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=excludes,
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

icon_path = os.path.join(project_root, '2.ico')

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='简单测试应用',
    debug=True,
    bootloader_ignore_signals=False,
    strip=False,
    upx=False,
    console=True,  # 显示控制台
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=icon_path if os.path.exists(icon_path) else None,
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=False,
    upx_exclude=[],
    name='简单测试应用',
)
'''
    
    with open('simple_test.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✅ 简单测试应用spec文件创建完成")

def main():
    """主函数"""
    print("🔧 创建调试版本和简单测试")
    print("=" * 50)
    
    # 创建调试控制台版spec
    create_debug_console_spec()
    
    # 创建简单测试应用
    create_simple_test_app()
    
    # 创建简单测试spec
    create_simple_test_spec()
    
    print("\n" + "=" * 50)
    print("🎉 调试工具创建完成！")
    print("=" * 50)
    print("💡 调试步骤:")
    print("1. 先测试简单版本:")
    print("   pack_env\\Scripts\\python.exe -m PyInstaller simple_test.spec")
    print("   python fix_qt_plugins.py")
    print("   运行: dist\\简单测试应用\\简单测试应用.exe")
    print("")
    print("2. 如果简单版本正常，再测试完整版本:")
    print("   pack_env\\Scripts\\python.exe -m PyInstaller debug_console.spec")
    print("   python fix_qt_plugins.py")
    print("   运行: dist\\企业微信自动回复系统_调试控制台版\\企业微信自动回复系统_调试控制台版.exe")
    print("")
    print("✅ 这样可以逐步定位问题所在！")

if __name__ == "__main__":
    main()
