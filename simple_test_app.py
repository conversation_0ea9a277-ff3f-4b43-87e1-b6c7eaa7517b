#!/usr/bin/env python3
# encoding:utf-8
"""
简单的测试应用 - 验证基础功能
"""

import sys
import os
import traceback

def main():
    """主函数"""
    print("🧪 企业微信自动回复系统 - 简单测试")
    print("=" * 50)
    
    try:
        print("1. 测试Python基础环境...")
        print(f"   Python版本: {sys.version}")
        print(f"   当前目录: {os.getcwd()}")
        print(f"   可执行文件: {sys.executable}")
        print("   ✅ Python环境正常")
        
        print("\n2. 测试PyQt5导入...")
        from PyQt5.QtWidgets import QApplication, QMainWindow, QLabel, QVBoxLayout, QWidget
        from PyQt5.QtCore import Qt
        from PyQt5.QtGui import QIcon
        print("   ✅ PyQt5导入成功")
        
        print("\n3. 测试嵌入式资源...")
        try:
            from common.embedded_resources import get_icon_path, get_contact_image_path
            icon_path = get_icon_path()
            image_path = get_contact_image_path()
            print(f"   图标路径: {icon_path}")
            print(f"   图片路径: {image_path}")
            print("   ✅ 嵌入式资源正常")
        except Exception as e:
            print(f"   ❌ 嵌入式资源错误: {e}")
            traceback.print_exc()
        
        print("\n4. 创建简单GUI窗口...")
        app = QApplication(sys.argv)
        
        window = QMainWindow()
        window.setWindowTitle("企业微信自动回复系统 - 测试窗口")
        window.setGeometry(100, 100, 400, 300)
        
        # 设置图标
        try:
            icon_path = get_icon_path()
            if icon_path and os.path.exists(icon_path):
                icon = QIcon(icon_path)
                window.setWindowIcon(icon)
                print("   ✅ 窗口图标设置成功")
            else:
                print("   ⚠️ 图标文件不存在，使用默认图标")
        except Exception as e:
            print(f"   ❌ 图标设置失败: {e}")
        
        # 创建内容
        central_widget = QWidget()
        window.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 标题
        title_label = QLabel("🎉 企业微信自动回复系统测试成功！")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #2c3e50; margin: 20px;")
        layout.addWidget(title_label)
        
        # 状态信息
        status_label = QLabel(f"""
✅ Python环境: {sys.version.split()[0]}
✅ PyQt5: 正常加载
✅ 嵌入式资源: 正常工作
✅ GUI界面: 正常显示
✅ 图标显示: {'成功' if icon_path and os.path.exists(icon_path) else '使用默认'}

🎯 如果您能看到这个窗口，说明基础环境完全正常！
""")
        status_label.setStyleSheet("font-size: 12px; color: #34495e; margin: 10px;")
        layout.addWidget(status_label)
        
        # 测试图片显示
        try:
            from PyQt5.QtGui import QPixmap
            image_path = get_contact_image_path()
            if image_path and os.path.exists(image_path):
                pixmap = QPixmap(image_path)
                if not pixmap.isNull():
                    image_label = QLabel()
                    scaled_pixmap = pixmap.scaled(200, 150, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                    image_label.setPixmap(scaled_pixmap)
                    image_label.setAlignment(Qt.AlignCenter)
                    layout.addWidget(image_label)
                    print("   ✅ 联系方式图片显示成功")
                else:
                    print("   ❌ 图片加载失败")
            else:
                print("   ❌ 图片文件不存在")
        except Exception as e:
            print(f"   ❌ 图片显示错误: {e}")
        
        print("\n5. 显示测试窗口...")
        window.show()
        print("   ✅ 测试窗口已显示")
        
        print("\n🎉 所有测试完成！窗口正在显示中...")
        print("💡 请检查:")
        print("   - 窗口是否正确显示")
        print("   - 窗口图标是否正确")
        print("   - 联系方式图片是否显示")
        
        # 运行应用
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        traceback.print_exc()
        input("\n按回车键退出...")
        return 1

if __name__ == "__main__":
    main()
