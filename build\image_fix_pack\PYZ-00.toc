('E:\\Desktop\\企业微信\\build\\image_fix_pack\\PYZ-00.pyz',
 [('pkg_resources',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pkg_resources\\__init__.py',
   'PYMODULE'),
  ('packaging.version',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('typing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\typing.py',
   'PYMODULE'),
  ('contextlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\contextlib.py',
   'PYMODULE'),
  ('__future__',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\__future__.py',
   'PYMODULE'),
  ('packaging.utils',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\packaging\\utils.py',
   'PYMODULE'),
  ('packaging.tags',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\packaging\\tags.py',
   'PYMODULE'),
  ('subprocess',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\subprocess.py',
   'PYMODULE'),
  ('selectors',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\selectors.py',
   'PYMODULE'),
  ('signal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\signal.py',
   'PYMODULE'),
  ('struct',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\struct.py',
   'PYMODULE'),
  ('packaging.specifiers',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\packaging\\specifiers.py',
   'PYMODULE'),
  ('packaging.requirements',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\packaging\\requirements.py',
   'PYMODULE'),
  ('packaging.metadata',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\packaging\\metadata.py',
   'PYMODULE'),
  ('email.policy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\policy.py',
   'PYMODULE'),
  ('email',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\__init__.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._encoded_words',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('base64',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\base64.py',
   'PYMODULE'),
  ('getopt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\getopt.py',
   'PYMODULE'),
  ('gettext',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\gettext.py',
   'PYMODULE'),
  ('copy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\copy.py',
   'PYMODULE'),
  ('string',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\string.py',
   'PYMODULE'),
  ('email.charset',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\charset.py',
   'PYMODULE'),
  ('email.encoders',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\encoders.py',
   'PYMODULE'),
  ('quopri',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\quopri.py',
   'PYMODULE'),
  ('email.quoprimime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.base64mime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.errors',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\errors.py',
   'PYMODULE'),
  ('email.contentmanager',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.headerregistry',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\utils.py',
   'PYMODULE'),
  ('email._parseaddr',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('calendar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\calendar.py',
   'PYMODULE'),
  ('argparse',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\argparse.py',
   'PYMODULE'),
  ('shutil',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\shutil.py',
   'PYMODULE'),
  ('tarfile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\tarfile.py',
   'PYMODULE'),
  ('gzip',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\gzip.py',
   'PYMODULE'),
  ('_compression',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\_compression.py',
   'PYMODULE'),
  ('lzma',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\lzma.py',
   'PYMODULE'),
  ('bz2',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\bz2.py',
   'PYMODULE'),
  ('datetime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\datetime.py',
   'PYMODULE'),
  ('_strptime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\_strptime.py',
   'PYMODULE'),
  ('socket',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\socket.py',
   'PYMODULE'),
  ('random',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\random.py',
   'PYMODULE'),
  ('statistics',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\statistics.py',
   'PYMODULE'),
  ('decimal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\decimal.py',
   'PYMODULE'),
  ('_pydecimal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\_pydecimal.py',
   'PYMODULE'),
  ('contextvars',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\contextvars.py',
   'PYMODULE'),
  ('fractions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\fractions.py',
   'PYMODULE'),
  ('numbers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\numbers.py',
   'PYMODULE'),
  ('hashlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\hashlib.py',
   'PYMODULE'),
  ('bisect',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\bisect.py',
   'PYMODULE'),
  ('email._policybase',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.message',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\message.py',
   'PYMODULE'),
  ('email.iterators',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.generator',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\generator.py',
   'PYMODULE'),
  ('uu',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\uu.py',
   'PYMODULE'),
  ('optparse',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\optparse.py',
   'PYMODULE'),
  ('email.header',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\header.py',
   'PYMODULE'),
  ('email.feedparser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\feedparser.py',
   'PYMODULE'),
  ('packaging.markers',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\packaging\\markers.py',
   'PYMODULE'),
  ('packaging.licenses._spdx',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\packaging\\licenses\\_spdx.py',
   'PYMODULE'),
  ('packaging.licenses',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\packaging\\licenses\\__init__.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('packaging._structures',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('packaging._parser',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\packaging\\_parser.py',
   'PYMODULE'),
  ('ast',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\ast.py',
   'PYMODULE'),
  ('packaging._musllinux',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('packaging._manylinux',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('packaging._elffile',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\packaging\\_elffile.py',
   'PYMODULE'),
  ('packaging',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pkg_resources\\_vendor\\pyparsing.py',
   'PYMODULE'),
  ('uuid',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\uuid.py',
   'PYMODULE'),
  ('pdb',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\pdb.py',
   'PYMODULE'),
  ('pydoc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\pydoc.py',
   'PYMODULE'),
  ('webbrowser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\webbrowser.py',
   'PYMODULE'),
  ('http.server',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\http\\server.py',
   'PYMODULE'),
  ('http',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\http\\__init__.py',
   'PYMODULE'),
  ('socketserver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\socketserver.py',
   'PYMODULE'),
  ('mimetypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\mimetypes.py',
   'PYMODULE'),
  ('http.client',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\http\\client.py',
   'PYMODULE'),
  ('ssl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\ssl.py',
   'PYMODULE'),
  ('html',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\html\\__init__.py',
   'PYMODULE'),
  ('html.entities',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\html\\entities.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pydoc_data',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('tty',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\tty.py',
   'PYMODULE'),
  ('importlib.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\util.py',
   'PYMODULE'),
  ('importlib._abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\_abc.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('csv',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\csv.py',
   'PYMODULE'),
  ('importlib.readers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('runpy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\runpy.py',
   'PYMODULE'),
  ('shlex',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\shlex.py',
   'PYMODULE'),
  ('glob',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\glob.py',
   'PYMODULE'),
  ('code',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\code.py',
   'PYMODULE'),
  ('codeop',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\codeop.py',
   'PYMODULE'),
  ('dis',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\dis.py',
   'PYMODULE'),
  ('opcode',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\opcode.py',
   'PYMODULE'),
  ('bdb',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\bdb.py',
   'PYMODULE'),
  ('cmd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\cmd.py',
   'PYMODULE'),
  ('pprint',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\pprint.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.version',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.utils',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.tags',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('distutils.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\distutils\\util.py',
   'PYMODULE'),
  ('distutils.filelist',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\distutils\\filelist.py',
   'PYMODULE'),
  ('distutils.debug',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\distutils\\debug.py',
   'PYMODULE'),
  ('distutils.file_util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\distutils\\file_util.py',
   'PYMODULE'),
  ('distutils.dir_util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\distutils\\dir_util.py',
   'PYMODULE'),
  ('py_compile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\py_compile.py',
   'PYMODULE'),
  ('distutils.sysconfig',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\distutils\\sysconfig.py',
   'PYMODULE'),
  ('distutils.text_file',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\distutils\\text_file.py',
   'PYMODULE'),
  ('_osx_support',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\_osx_support.py',
   'PYMODULE'),
  ('_aix_support',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\_aix_support.py',
   'PYMODULE'),
  ('_bootsubprocess',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\_bootsubprocess.py',
   'PYMODULE'),
  ('distutils.log',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\distutils\\log.py',
   'PYMODULE'),
  ('distutils.spawn',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\distutils\\spawn.py',
   'PYMODULE'),
  ('distutils.dep_util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\distutils\\dep_util.py',
   'PYMODULE'),
  ('distutils.errors',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\distutils\\errors.py',
   'PYMODULE'),
  ('distutils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\distutils\\__init__.py',
   'PYMODULE'),
  ('distutils.archive_util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\distutils\\archive_util.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.specifiers',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.requirements',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.markers',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._typing',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_typing.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._structures',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._compat',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_compat.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.__about__',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\__about__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.appdirs',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pkg_resources\\_vendor\\appdirs.py',
   'PYMODULE'),
  ('win32com.shell.shellcon',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\win32comext\\shell\\shellcon.py',
   'PYMODULE'),
  ('win32com.shell',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\win32comext\\shell\\__init__.py',
   'PYMODULE'),
  ('win32com',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\win32com\\__init__.py',
   'PYMODULE'),
  ('win32com.server.util',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\win32com\\server\\util.py',
   'PYMODULE'),
  ('win32com.server.dispatcher',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\win32com\\server\\dispatcher.py',
   'PYMODULE'),
  ('win32traceutil',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\win32\\lib\\win32traceutil.py',
   'PYMODULE'),
  ('win32com.util',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\win32com\\util.py',
   'PYMODULE'),
  ('win32com.server.exception',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\win32com\\server\\exception.py',
   'PYMODULE'),
  ('win32com.server.policy',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\win32com\\server\\policy.py',
   'PYMODULE'),
  ('win32com.client',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\win32com\\client\\__init__.py',
   'PYMODULE'),
  ('win32com.client.makepy',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\win32com\\client\\makepy.py',
   'PYMODULE'),
  ('pywin.dialogs.status',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\Pythonwin\\pywin\\dialogs\\status.py',
   'PYMODULE'),
  ('pywin.mfc.thread',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\Pythonwin\\pywin\\mfc\\thread.py',
   'PYMODULE'),
  ('pywin.mfc.object',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\Pythonwin\\pywin\\mfc\\object.py',
   'PYMODULE'),
  ('pywin.mfc.dialog',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\Pythonwin\\pywin\\mfc\\dialog.py',
   'PYMODULE'),
  ('pywin.mfc.window',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\Pythonwin\\pywin\\mfc\\window.py',
   'PYMODULE'),
  ('pywin.mfc',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\Pythonwin\\pywin\\mfc\\__init__.py',
   'PYMODULE'),
  ('pywin.dialogs',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\Pythonwin\\pywin\\dialogs\\__init__.py',
   'PYMODULE'),
  ('pywin',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\Pythonwin\\pywin\\__init__.py',
   'PYMODULE'),
  ('win32com.client.selecttlb',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\win32com\\client\\selecttlb.py',
   'PYMODULE'),
  ('pywin.dialogs.list',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\Pythonwin\\pywin\\dialogs\\list.py',
   'PYMODULE'),
  ('commctrl',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\win32\\lib\\commctrl.py',
   'PYMODULE'),
  ('win32com.client.genpy',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\win32com\\client\\genpy.py',
   'PYMODULE'),
  ('win32com.client.gencache',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\win32com\\client\\gencache.py',
   'PYMODULE'),
  ('pickle',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\pickle.py',
   'PYMODULE'),
  ('_compat_pickle',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\_compat_pickle.py',
   'PYMODULE'),
  ('win32com.client.CLSIDToClass',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\win32com\\client\\CLSIDToClass.py',
   'PYMODULE'),
  ('win32com.client.dynamic',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\win32com\\client\\dynamic.py',
   'PYMODULE'),
  ('win32com.client.build',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\win32com\\client\\build.py',
   'PYMODULE'),
  ('win32com.server',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\win32com\\server\\__init__.py',
   'PYMODULE'),
  ('winerror',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\win32\\lib\\winerror.py',
   'PYMODULE'),
  ('win32com.universal',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\win32com\\universal.py',
   'PYMODULE'),
  ('win32com.client.util',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\win32com\\client\\util.py',
   'PYMODULE'),
  ('pythoncom',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pythoncom.py',
   'PYMODULE'),
  ('pkg_resources._vendor',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pkg_resources\\_vendor\\__init__.py',
   'PYMODULE'),
  ('sysconfig',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\sysconfig.py',
   'PYMODULE'),
  ('pkg_resources.extern',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pkg_resources\\extern\\__init__.py',
   'PYMODULE'),
  ('importlib.machinery',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('imp',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\imp.py',
   'PYMODULE'),
  ('importlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._common',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\_common.py',
   'PYMODULE'),
  ('importlib._adapters',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\_adapters.py',
   'PYMODULE'),
  ('inspect',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\inspect.py',
   'PYMODULE'),
  ('textwrap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\textwrap.py',
   'PYMODULE'),
  ('tempfile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\tempfile.py',
   'PYMODULE'),
  ('email.parser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\parser.py',
   'PYMODULE'),
  ('plistlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\plistlib.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.parsers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\xml\\__init__.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('xml.sax',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('platform',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\platform.py',
   'PYMODULE'),
  ('pkgutil',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\pkgutil.py',
   'PYMODULE'),
  ('zipimport',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\zipimport.py',
   'PYMODULE'),
  ('zipfile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\zipfile.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('xmlrpc.client',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('xmlrpc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\xmlrpc\\__init__.py',
   'PYMODULE'),
  ('hmac',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\hmac.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('secrets',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\secrets.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('dataclasses',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\dataclasses.py',
   'PYMODULE'),
  ('typing_extensions',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.log',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\log.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.constants',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\__init__.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.threads',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\threads.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.streams',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.queues',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('concurrent.futures',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.tasks',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.locks',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.mixins',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\mixins.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.transports',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.futures',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\events.py',
   'PYMODULE'),
  ('pywintypes',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\win32\\lib\\pywintypes.py',
   'PYMODULE'),
  ('pywin32_system32', '-', 'PYMODULE'),
  ('win32con',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\win32\\lib\\win32con.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('ctypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes._endian',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('logging',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\logging\\__init__.py',
   'PYMODULE'),
  ('configparser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\configparser.py',
   'PYMODULE'),
  ('sqlite3',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\sqlite3\\__init__.py',
   'PYMODULE'),
  ('sqlite3.dump',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\sqlite3\\dump.py',
   'PYMODULE'),
  ('sqlite3.dbapi2',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\sqlite3\\dbapi2.py',
   'PYMODULE'),
  ('queue',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\queue.py',
   'PYMODULE'),
  ('threading',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\threading.py',
   'PYMODULE'),
  ('_threading_local',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\_threading_local.py',
   'PYMODULE'),
  ('json',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\json\\__init__.py',
   'PYMODULE'),
  ('json.encoder',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.decoder',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.scanner',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\json\\scanner.py',
   'PYMODULE'),
  ('requests',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\requests\\__init__.py',
   'PYMODULE'),
  ('requests.status_codes',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\requests\\status_codes.py',
   'PYMODULE'),
  ('requests.structures',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\requests\\structures.py',
   'PYMODULE'),
  ('requests.compat',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\requests\\compat.py',
   'PYMODULE'),
  ('http.cookies',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\http\\cookies.py',
   'PYMODULE'),
  ('http.cookiejar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('requests.models',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\requests\\models.py',
   'PYMODULE'),
  ('idna',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\idna\\__init__.py',
   'PYMODULE'),
  ('idna.package_data',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('idna.intranges',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('idna.core',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\idna\\core.py',
   'PYMODULE'),
  ('idna.uts46data',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('idna.idnadata',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('requests.hooks',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\requests\\hooks.py',
   'PYMODULE'),
  ('requests.cookies',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\requests\\cookies.py',
   'PYMODULE'),
  ('requests.auth',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\requests\\auth.py',
   'PYMODULE'),
  ('requests._internal_utils',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\requests\\_internal_utils.py',
   'PYMODULE'),
  ('urllib3.util',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\urllib3\\util\\__init__.py',
   'PYMODULE'),
  ('urllib3.util.wait',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\urllib3\\util\\wait.py',
   'PYMODULE'),
  ('urllib3.util.url',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\urllib3\\util\\url.py',
   'PYMODULE'),
  ('urllib3.util.util',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\urllib3\\util\\util.py',
   'PYMODULE'),
  ('urllib3.util.timeout',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\urllib3\\util\\timeout.py',
   'PYMODULE'),
  ('urllib3.util.ssl_',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\urllib3\\util\\ssl_.py',
   'PYMODULE'),
  ('urllib3.util.ssltransport',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\urllib3\\util\\ssltransport.py',
   'PYMODULE'),
  ('urllib3.util.retry',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\urllib3\\util\\retry.py',
   'PYMODULE'),
  ('urllib3.response',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\urllib3\\response.py',
   'PYMODULE'),
  ('urllib3.connection',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\urllib3\\connection.py',
   'PYMODULE'),
  ('urllib3.util.ssl_match_hostname',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\urllib3\\util\\ssl_match_hostname.py',
   'PYMODULE'),
  ('ipaddress',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\ipaddress.py',
   'PYMODULE'),
  ('urllib3._version',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\urllib3\\_version.py',
   'PYMODULE'),
  ('urllib3.http2.probe',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\urllib3\\http2\\probe.py',
   'PYMODULE'),
  ('urllib3.http2',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\urllib3\\http2\\__init__.py',
   'PYMODULE'),
  ('urllib3.http2.connection',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\urllib3\\http2\\connection.py',
   'PYMODULE'),
  ('urllib3._collections',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\urllib3\\_collections.py',
   'PYMODULE'),
  ('urllib3._base_connection',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\urllib3\\_base_connection.py',
   'PYMODULE'),
  ('urllib3.connectionpool',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\urllib3\\connectionpool.py',
   'PYMODULE'),
  ('urllib3.util.proxy',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\urllib3\\util\\proxy.py',
   'PYMODULE'),
  ('urllib3._request_methods',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\urllib3\\_request_methods.py',
   'PYMODULE'),
  ('urllib3.util.response',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\urllib3\\util\\response.py',
   'PYMODULE'),
  ('urllib3.util.request',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\urllib3\\util\\request.py',
   'PYMODULE'),
  ('urllib3.util.connection',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\urllib3\\util\\connection.py',
   'PYMODULE'),
  ('urllib3.filepost',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\urllib3\\filepost.py',
   'PYMODULE'),
  ('urllib3.fields',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\urllib3\\fields.py',
   'PYMODULE'),
  ('requests.api',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\requests\\api.py',
   'PYMODULE'),
  ('requests.sessions',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\requests\\sessions.py',
   'PYMODULE'),
  ('requests.adapters',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\requests\\adapters.py',
   'PYMODULE'),
  ('urllib3.contrib.socks',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\urllib3\\contrib\\socks.py',
   'PYMODULE'),
  ('urllib3.poolmanager',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\urllib3\\poolmanager.py',
   'PYMODULE'),
  ('requests.__version__',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\requests\\__version__.py',
   'PYMODULE'),
  ('requests.utils',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\requests\\utils.py',
   'PYMODULE'),
  ('netrc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\netrc.py',
   'PYMODULE'),
  ('requests.certs',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\requests\\certs.py',
   'PYMODULE'),
  ('certifi',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\certifi\\__init__.py',
   'PYMODULE'),
  ('certifi.core',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\certifi\\core.py',
   'PYMODULE'),
  ('importlib.resources',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\resources.py',
   'PYMODULE'),
  ('requests.packages',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\requests\\packages.py',
   'PYMODULE'),
  ('urllib3.exceptions',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\urllib3\\exceptions.py',
   'PYMODULE'),
  ('urllib3.contrib.pyopenssl',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\urllib3\\contrib\\pyopenssl.py',
   'PYMODULE'),
  ('urllib3.contrib',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\urllib3\\contrib\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('requests.exceptions',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\requests\\exceptions.py',
   'PYMODULE'),
  ('urllib3',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\urllib3\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\urllib3\\contrib\\emscripten\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.connection',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\urllib3\\contrib\\emscripten\\connection.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.response',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\urllib3\\contrib\\emscripten\\response.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.request',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\urllib3\\contrib\\emscripten\\request.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.fetch',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\urllib3\\contrib\\emscripten\\fetch.py',
   'PYMODULE'),
  ('plugins.plugin_manager',
   'E:\\Desktop\\企业微信\\plugins\\plugin_manager.py',
   'PYMODULE'),
  ('plugins', 'E:\\Desktop\\企业微信\\plugins\\__init__.py', 'PYMODULE'),
  ('plugins.plugin', 'E:\\Desktop\\企业微信\\plugins\\plugin.py', 'PYMODULE'),
  ('common.package_manager',
   'E:\\Desktop\\企业微信\\common\\package_manager.py',
   'PYMODULE'),
  ('common', '-', 'PYMODULE'),
  ('common.memory', 'E:\\Desktop\\企业微信\\common\\memory.py', 'PYMODULE'),
  ('common.expired_dict',
   'E:\\Desktop\\企业微信\\common\\expired_dict.py',
   'PYMODULE'),
  ('common.const', 'E:\\Desktop\\企业微信\\common\\const.py', 'PYMODULE'),
  ('pip._internal.main',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_internal\\main.py',
   'PYMODULE'),
  ('pip._internal.utils.entrypoints',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_internal\\utils\\entrypoints.py',
   'PYMODULE'),
  ('pip._internal.utils',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_internal\\utils\\__init__.py',
   'PYMODULE'),
  ('pip._internal.utils.appdirs',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_internal\\utils\\appdirs.py',
   'PYMODULE'),
  ('pip._vendor.appdirs',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\appdirs.py',
   'PYMODULE'),
  ('pip._vendor',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\__init__.py',
   'PYMODULE'),
  ('pip._vendor.distro',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\distro.py',
   'PYMODULE'),
  ('pip._vendor.html5lib',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\html5lib\\__init__.py',
   'PYMODULE'),
  ('pip._vendor.html5lib.serializer',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\html5lib\\serializer.py',
   'PYMODULE'),
  ('pip._vendor.html5lib.filters.optionaltags',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\html5lib\\filters\\optionaltags.py',
   'PYMODULE'),
  ('pip._vendor.html5lib.filters.base',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\html5lib\\filters\\base.py',
   'PYMODULE'),
  ('pip._vendor.html5lib.filters',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\html5lib\\filters\\__init__.py',
   'PYMODULE'),
  ('pip._vendor.html5lib.filters.sanitizer',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\html5lib\\filters\\sanitizer.py',
   'PYMODULE'),
  ('pip._vendor.html5lib.filters.whitespace',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\html5lib\\filters\\whitespace.py',
   'PYMODULE'),
  ('pip._vendor.html5lib.filters.alphabeticalattributes',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\html5lib\\filters\\alphabeticalattributes.py',
   'PYMODULE'),
  ('pip._vendor.html5lib.filters.inject_meta_charset',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\html5lib\\filters\\inject_meta_charset.py',
   'PYMODULE'),
  ('pip._vendor.html5lib.constants',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\html5lib\\constants.py',
   'PYMODULE'),
  ('pip._vendor.html5lib.treewalkers',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\html5lib\\treewalkers\\__init__.py',
   'PYMODULE'),
  ('pip._vendor.html5lib.treewalkers.etree',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\html5lib\\treewalkers\\etree.py',
   'PYMODULE'),
  ('pip._vendor.html5lib.treewalkers.etree_lxml',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\html5lib\\treewalkers\\etree_lxml.py',
   'PYMODULE'),
  ('pip._vendor.html5lib.treebuilders.etree',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\html5lib\\treebuilders\\etree.py',
   'PYMODULE'),
  ('pip._vendor.html5lib.treebuilders.base',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\html5lib\\treebuilders\\base.py',
   'PYMODULE'),
  ('pip._vendor.html5lib.treewalkers.genshi',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\html5lib\\treewalkers\\genshi.py',
   'PYMODULE'),
  ('pip._vendor.html5lib.treewalkers.dom',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\html5lib\\treewalkers\\dom.py',
   'PYMODULE'),
  ('xml.dom',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\xml\\dom\\__init__.py',
   'PYMODULE'),
  ('xml.dom.pulldom',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\xml\\dom\\pulldom.py',
   'PYMODULE'),
  ('xml.dom.minidom',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\xml\\dom\\minidom.py',
   'PYMODULE'),
  ('xml.dom.expatbuilder',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\xml\\dom\\expatbuilder.py',
   'PYMODULE'),
  ('xml.dom.NodeFilter',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\xml\\dom\\NodeFilter.py',
   'PYMODULE'),
  ('xml.dom.minicompat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\xml\\dom\\minicompat.py',
   'PYMODULE'),
  ('xml.dom.xmlbuilder',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\xml\\dom\\xmlbuilder.py',
   'PYMODULE'),
  ('xml.dom.domreg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\xml\\dom\\domreg.py',
   'PYMODULE'),
  ('pip._vendor.html5lib.treewalkers.base',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\html5lib\\treewalkers\\base.py',
   'PYMODULE'),
  ('pip._vendor.html5lib.html5parser',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\html5lib\\html5parser.py',
   'PYMODULE'),
  ('pip._vendor.html5lib.treebuilders',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\html5lib\\treebuilders\\__init__.py',
   'PYMODULE'),
  ('pip._vendor.html5lib.treebuilders.etree_lxml',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\html5lib\\treebuilders\\etree_lxml.py',
   'PYMODULE'),
  ('pip._vendor.html5lib.treebuilders.dom',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\html5lib\\treebuilders\\dom.py',
   'PYMODULE'),
  ('pip._vendor.html5lib._ihatexml',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\html5lib\\_ihatexml.py',
   'PYMODULE'),
  ('pip._vendor.html5lib._tokenizer',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\html5lib\\_tokenizer.py',
   'PYMODULE'),
  ('pip._vendor.html5lib._trie',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\html5lib\\_trie\\__init__.py',
   'PYMODULE'),
  ('pip._vendor.html5lib._trie.py',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\html5lib\\_trie\\py.py',
   'PYMODULE'),
  ('pip._vendor.html5lib._trie._base',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\html5lib\\_trie\\_base.py',
   'PYMODULE'),
  ('pip._vendor.html5lib._inputstream',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\html5lib\\_inputstream.py',
   'PYMODULE'),
  ('pip._vendor.chardet.universaldetector',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\chardet\\universaldetector.py',
   'PYMODULE'),
  ('pip._vendor.chardet',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\chardet\\__init__.py',
   'PYMODULE'),
  ('pip._vendor.chardet.version',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\chardet\\version.py',
   'PYMODULE'),
  ('pip._vendor.chardet.sbcsgroupprober',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\chardet\\sbcsgroupprober.py',
   'PYMODULE'),
  ('pip._vendor.chardet.sbcharsetprober',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\chardet\\sbcharsetprober.py',
   'PYMODULE'),
  ('pip._vendor.chardet.charsetprober',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\chardet\\charsetprober.py',
   'PYMODULE'),
  ('pip._vendor.chardet.langturkishmodel',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\chardet\\langturkishmodel.py',
   'PYMODULE'),
  ('pip._vendor.chardet.langthaimodel',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\chardet\\langthaimodel.py',
   'PYMODULE'),
  ('pip._vendor.chardet.langrussianmodel',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\chardet\\langrussianmodel.py',
   'PYMODULE'),
  ('pip._vendor.chardet.langhebrewmodel',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\chardet\\langhebrewmodel.py',
   'PYMODULE'),
  ('pip._vendor.chardet.langgreekmodel',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\chardet\\langgreekmodel.py',
   'PYMODULE'),
  ('pip._vendor.chardet.langbulgarianmodel',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\chardet\\langbulgarianmodel.py',
   'PYMODULE'),
  ('pip._vendor.chardet.hebrewprober',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\chardet\\hebrewprober.py',
   'PYMODULE'),
  ('pip._vendor.chardet.mbcsgroupprober',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\chardet\\mbcsgroupprober.py',
   'PYMODULE'),
  ('pip._vendor.chardet.euctwprober',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\chardet\\euctwprober.py',
   'PYMODULE'),
  ('pip._vendor.chardet.mbcssm',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\chardet\\mbcssm.py',
   'PYMODULE'),
  ('pip._vendor.chardet.chardistribution',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\chardet\\chardistribution.py',
   'PYMODULE'),
  ('pip._vendor.chardet.jisfreq',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\chardet\\jisfreq.py',
   'PYMODULE'),
  ('pip._vendor.chardet.big5freq',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\chardet\\big5freq.py',
   'PYMODULE'),
  ('pip._vendor.chardet.gb2312freq',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\chardet\\gb2312freq.py',
   'PYMODULE'),
  ('pip._vendor.chardet.euckrfreq',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\chardet\\euckrfreq.py',
   'PYMODULE'),
  ('pip._vendor.chardet.euctwfreq',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\chardet\\euctwfreq.py',
   'PYMODULE'),
  ('pip._vendor.chardet.codingstatemachine',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\chardet\\codingstatemachine.py',
   'PYMODULE'),
  ('pip._vendor.chardet.mbcharsetprober',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\chardet\\mbcharsetprober.py',
   'PYMODULE'),
  ('pip._vendor.chardet.big5prober',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\chardet\\big5prober.py',
   'PYMODULE'),
  ('pip._vendor.chardet.cp949prober',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\chardet\\cp949prober.py',
   'PYMODULE'),
  ('pip._vendor.chardet.euckrprober',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\chardet\\euckrprober.py',
   'PYMODULE'),
  ('pip._vendor.chardet.gb2312prober',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\chardet\\gb2312prober.py',
   'PYMODULE'),
  ('pip._vendor.chardet.eucjpprober',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\chardet\\eucjpprober.py',
   'PYMODULE'),
  ('pip._vendor.chardet.jpcntx',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\chardet\\jpcntx.py',
   'PYMODULE'),
  ('pip._vendor.chardet.sjisprober',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\chardet\\sjisprober.py',
   'PYMODULE'),
  ('pip._vendor.chardet.utf8prober',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\chardet\\utf8prober.py',
   'PYMODULE'),
  ('pip._vendor.chardet.latin1prober',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\chardet\\latin1prober.py',
   'PYMODULE'),
  ('pip._vendor.chardet.escprober',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\chardet\\escprober.py',
   'PYMODULE'),
  ('pip._vendor.chardet.escsm',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\chardet\\escsm.py',
   'PYMODULE'),
  ('pip._vendor.chardet.enums',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\chardet\\enums.py',
   'PYMODULE'),
  ('pip._vendor.chardet.charsetgroupprober',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\chardet\\charsetgroupprober.py',
   'PYMODULE'),
  ('pip._vendor.html5lib._utils',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\html5lib\\_utils.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.etree',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\xml\\etree\\__init__.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.ElementInclude',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('pip._vendor.webencodings',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\webencodings\\__init__.py',
   'PYMODULE'),
  ('pip._vendor.webencodings.x_user_defined',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\webencodings\\x_user_defined.py',
   'PYMODULE'),
  ('pip._vendor.webencodings.labels',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\webencodings\\labels.py',
   'PYMODULE'),
  ('pip._vendor.msgpack',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\msgpack\\__init__.py',
   'PYMODULE'),
  ('pip._vendor.msgpack.fallback',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\msgpack\\fallback.py',
   'PYMODULE'),
  ('pip._vendor.msgpack.ext',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\msgpack\\ext.py',
   'PYMODULE'),
  ('pip._vendor.msgpack.exceptions',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\msgpack\\exceptions.py',
   'PYMODULE'),
  ('pip._vendor.msgpack._version',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\msgpack\\_version.py',
   'PYMODULE'),
  ('pip._vendor.colorama',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\colorama\\__init__.py',
   'PYMODULE'),
  ('pip._vendor.colorama.ansitowin32',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\colorama\\ansitowin32.py',
   'PYMODULE'),
  ('pip._vendor.colorama.winterm',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\colorama\\winterm.py',
   'PYMODULE'),
  ('pip._vendor.colorama.ansi',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\colorama\\ansi.py',
   'PYMODULE'),
  ('pip._vendor.colorama.initialise',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\colorama\\initialise.py',
   'PYMODULE'),
  ('pip._vendor.colorama.win32',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\colorama\\win32.py',
   'PYMODULE'),
  ('pip._vendor.six',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\six.py',
   'PYMODULE'),
  ('pip._vendor.idna',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\idna\\__init__.py',
   'PYMODULE'),
  ('pip._vendor.idna.intranges',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\idna\\intranges.py',
   'PYMODULE'),
  ('pip._vendor.idna.core',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\idna\\core.py',
   'PYMODULE'),
  ('pip._vendor.idna.uts46data',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\idna\\uts46data.py',
   'PYMODULE'),
  ('pip._vendor.idna.idnadata',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\idna\\idnadata.py',
   'PYMODULE'),
  ('pip._vendor.idna.package_data',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\idna\\package_data.py',
   'PYMODULE'),
  ('pip._internal.utils._log',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_internal\\utils\\_log.py',
   'PYMODULE'),
  ('pip._internal.cli.main',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_internal\\cli\\main.py',
   'PYMODULE'),
  ('pip._internal.cli',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_internal\\cli\\__init__.py',
   'PYMODULE'),
  ('pip._internal.cli.cmdoptions',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_internal\\cli\\cmdoptions.py',
   'PYMODULE'),
  ('pip._internal.utils.misc',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_internal\\utils\\misc.py',
   'PYMODULE'),
  ('pip._internal.metadata.pkg_resources',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_internal\\metadata\\pkg_resources.py',
   'PYMODULE'),
  ('pip._internal.metadata.base',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_internal\\metadata\\base.py',
   'PYMODULE'),
  ('pip._internal.models.direct_url',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_internal\\models\\direct_url.py',
   'PYMODULE'),
  ('pip._internal.models',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_internal\\models\\__init__.py',
   'PYMODULE'),
  ('pip._internal.utils.wheel',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_internal\\utils\\wheel.py',
   'PYMODULE'),
  ('pip._internal.utils.pkg_resources',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_internal\\utils\\pkg_resources.py',
   'PYMODULE'),
  ('pip._internal.utils.packaging',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_internal\\utils\\packaging.py',
   'PYMODULE'),
  ('pip._vendor.packaging.specifiers',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('pip._vendor.packaging',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('pip._vendor.packaging._musllinux',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('pip._vendor.packaging._manylinux',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('pip._vendor.packaging.__about__',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\packaging\\__about__.py',
   'PYMODULE'),
  ('pip._vendor.packaging.version',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('pip._vendor.packaging._structures',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('pip._vendor.packaging.requirements',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('pip._vendor.packaging.markers',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('pip._vendor.pyparsing',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\pyparsing.py',
   'PYMODULE'),
  ('pip._internal.metadata',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_internal\\metadata\\__init__.py',
   'PYMODULE'),
  ('pip._internal.utils.virtualenv',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_internal\\utils\\virtualenv.py',
   'PYMODULE'),
  ('site',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site.py',
   'PYMODULE'),
  ('rlcompleter',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\rlcompleter.py',
   'PYMODULE'),
  ('_sitebuiltins',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\_sitebuiltins.py',
   'PYMODULE'),
  ('pip._internal.utils.compat',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_internal\\utils\\compat.py',
   'PYMODULE'),
  ('pip._vendor.urllib3.util',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\urllib3\\util\\__init__.py',
   'PYMODULE'),
  ('pip._vendor.urllib3',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\urllib3\\__init__.py',
   'PYMODULE'),
  ('pip._vendor.urllib3.response',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\urllib3\\response.py',
   'PYMODULE'),
  ('pip._vendor.urllib3.packages.six',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\urllib3\\packages\\six.py',
   'PYMODULE'),
  ('pip._vendor.urllib3.packages',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\urllib3\\packages\\__init__.py',
   'PYMODULE'),
  ('pip._vendor.urllib3.packages.ssl_match_hostname',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\urllib3\\packages\\ssl_match_hostname\\__init__.py',
   'PYMODULE'),
  ('pip._vendor.urllib3.packages.ssl_match_hostname._implementation',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\urllib3\\packages\\ssl_match_hostname\\_implementation.py',
   'PYMODULE'),
  ('pip._vendor.urllib3.connection',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\urllib3\\connection.py',
   'PYMODULE'),
  ('pip._vendor.urllib3.util.proxy',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\urllib3\\util\\proxy.py',
   'PYMODULE'),
  ('pip._vendor.urllib3._collections',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\urllib3\\_collections.py',
   'PYMODULE'),
  ('pip._vendor.urllib3.poolmanager',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\urllib3\\poolmanager.py',
   'PYMODULE'),
  ('pip._vendor.urllib3.request',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\urllib3\\request.py',
   'PYMODULE'),
  ('pip._vendor.urllib3.filepost',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\urllib3\\filepost.py',
   'PYMODULE'),
  ('pip._vendor.urllib3.fields',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\urllib3\\fields.py',
   'PYMODULE'),
  ('pip._vendor.urllib3.connectionpool',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\urllib3\\connectionpool.py',
   'PYMODULE'),
  ('pip._vendor.urllib3.util.queue',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\urllib3\\util\\queue.py',
   'PYMODULE'),
  ('pip._vendor.urllib3._version',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\urllib3\\_version.py',
   'PYMODULE'),
  ('pip._vendor.urllib3.exceptions',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\urllib3\\exceptions.py',
   'PYMODULE'),
  ('pip._vendor.urllib3.util.wait',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\urllib3\\util\\wait.py',
   'PYMODULE'),
  ('pip._vendor.urllib3.util.url',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\urllib3\\util\\url.py',
   'PYMODULE'),
  ('pip._vendor.urllib3.util.timeout',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\urllib3\\util\\timeout.py',
   'PYMODULE'),
  ('pip._vendor.urllib3.util.ssl_',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\urllib3\\util\\ssl_.py',
   'PYMODULE'),
  ('pip._vendor.urllib3.util.ssltransport',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\urllib3\\util\\ssltransport.py',
   'PYMODULE'),
  ('pip._vendor.urllib3.util.retry',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\urllib3\\util\\retry.py',
   'PYMODULE'),
  ('pip._vendor.urllib3.util.response',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\urllib3\\util\\response.py',
   'PYMODULE'),
  ('pip._vendor.urllib3.util.request',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\urllib3\\util\\request.py',
   'PYMODULE'),
  ('pip._vendor.urllib3.util.connection',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\urllib3\\util\\connection.py',
   'PYMODULE'),
  ('pip._vendor.urllib3.contrib._appengine_environ',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\urllib3\\contrib\\_appengine_environ.py',
   'PYMODULE'),
  ('pip._vendor.urllib3.contrib',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\urllib3\\contrib\\__init__.py',
   'PYMODULE'),
  ('pip._vendor.urllib3.contrib.pyopenssl',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\urllib3\\contrib\\pyopenssl.py',
   'PYMODULE'),
  ('pip._vendor.urllib3.packages.backports.makefile',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\urllib3\\packages\\backports\\makefile.py',
   'PYMODULE'),
  ('pip._vendor.urllib3.packages.backports',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\urllib3\\packages\\backports\\__init__.py',
   'PYMODULE'),
  ('pip._vendor.urllib3.contrib.securetransport',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\urllib3\\contrib\\securetransport.py',
   'PYMODULE'),
  ('pip._vendor.urllib3.contrib._securetransport.low_level',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\urllib3\\contrib\\_securetransport\\low_level.py',
   'PYMODULE'),
  ('pip._vendor.urllib3.contrib._securetransport',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\urllib3\\contrib\\_securetransport\\__init__.py',
   'PYMODULE'),
  ('pip._vendor.urllib3.contrib._securetransport.bindings',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\urllib3\\contrib\\_securetransport\\bindings.py',
   'PYMODULE'),
  ('ctypes.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\ctypes\\util.py',
   'PYMODULE'),
  ('ctypes._aix',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\ctypes\\_aix.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('pip._vendor.tenacity.retry',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\tenacity\\retry.py',
   'PYMODULE'),
  ('pip._vendor.tenacity',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\tenacity\\__init__.py',
   'PYMODULE'),
  ('pip._vendor.tenacity.tornadoweb',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\tenacity\\tornadoweb.py',
   'PYMODULE'),
  ('pip._vendor.tenacity._asyncio',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\tenacity\\_asyncio.py',
   'PYMODULE'),
  ('pip._vendor.tenacity.before_sleep',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\tenacity\\before_sleep.py',
   'PYMODULE'),
  ('pip._vendor.tenacity.after',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\tenacity\\after.py',
   'PYMODULE'),
  ('pip._vendor.tenacity.before',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\tenacity\\before.py',
   'PYMODULE'),
  ('pip._vendor.tenacity.wait',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\tenacity\\wait.py',
   'PYMODULE'),
  ('pip._vendor.tenacity._utils',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\tenacity\\_utils.py',
   'PYMODULE'),
  ('pip._vendor.tenacity.stop',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\tenacity\\stop.py',
   'PYMODULE'),
  ('pip._vendor.tenacity.nap',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\tenacity\\nap.py',
   'PYMODULE'),
  ('pip._vendor.pkg_resources',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\pkg_resources\\__init__.py',
   'PYMODULE'),
  ('pip._vendor.pkg_resources.py31compat',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\pkg_resources\\py31compat.py',
   'PYMODULE'),
  ('getpass',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\getpass.py',
   'PYMODULE'),
  ('pip._internal.utils.hashes',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_internal\\utils\\hashes.py',
   'PYMODULE'),
  ('pip._internal.models.target_python',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_internal\\models\\target_python.py',
   'PYMODULE'),
  ('pip._internal.utils.compatibility_tags',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_internal\\utils\\compatibility_tags.py',
   'PYMODULE'),
  ('pip._vendor.packaging.tags',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('pip._internal.models.index',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_internal\\models\\index.py',
   'PYMODULE'),
  ('pip._internal.models.format_control',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_internal\\models\\format_control.py',
   'PYMODULE'),
  ('pip._internal.locations',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_internal\\locations\\__init__.py',
   'PYMODULE'),
  ('distutils.dist',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\distutils\\dist.py',
   'PYMODULE'),
  ('distutils.versionpredicate',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\distutils\\versionpredicate.py',
   'PYMODULE'),
  ('distutils.version',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\distutils\\version.py',
   'PYMODULE'),
  ('distutils.command',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\distutils\\command\\__init__.py',
   'PYMODULE'),
  ('distutils.core',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\distutils\\core.py',
   'PYMODULE'),
  ('distutils.extension',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\distutils\\extension.py',
   'PYMODULE'),
  ('distutils.config',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\distutils\\config.py',
   'PYMODULE'),
  ('cgi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\cgi.py',
   'PYMODULE'),
  ('distutils.cmd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\distutils\\cmd.py',
   'PYMODULE'),
  ('distutils.fancy_getopt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('distutils.command.install',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\distutils\\command\\install.py',
   'PYMODULE'),
  ('pip._internal.locations.base',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_internal\\locations\\base.py',
   'PYMODULE'),
  ('pip._internal.locations._sysconfig',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_internal\\locations\\_sysconfig.py',
   'PYMODULE'),
  ('pip._internal.locations._distutils',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_internal\\locations\\_distutils.py',
   'PYMODULE'),
  ('pip._internal.models.scheme',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_internal\\models\\scheme.py',
   'PYMODULE'),
  ('pip._internal.cli.progress_bars',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_internal\\cli\\progress_bars.py',
   'PYMODULE'),
  ('pip._internal.utils.logging',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_internal\\utils\\logging.py',
   'PYMODULE'),
  ('logging.handlers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\logging\\handlers.py',
   'PYMODULE'),
  ('win32evtlogutil',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\win32\\lib\\win32evtlogutil.py',
   'PYMODULE'),
  ('smtplib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\smtplib.py',
   'PYMODULE'),
  ('pip._vendor.progress.spinner',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\progress\\spinner.py',
   'PYMODULE'),
  ('pip._vendor.progress',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\progress\\__init__.py',
   'PYMODULE'),
  ('pip._vendor.progress.bar',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\progress\\bar.py',
   'PYMODULE'),
  ('pip._internal.cli.parser',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_internal\\cli\\parser.py',
   'PYMODULE'),
  ('pip._internal.configuration',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_internal\\configuration.py',
   'PYMODULE'),
  ('pip._internal.cli.status_codes',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_internal\\cli\\status_codes.py',
   'PYMODULE'),
  ('pip._vendor.packaging.utils',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('pip._internal.utils.deprecation',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_internal\\utils\\deprecation.py',
   'PYMODULE'),
  ('pip._internal.exceptions',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_internal\\exceptions.py',
   'PYMODULE'),
  ('pip._internal.req.req_install',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_internal\\req\\req_install.py',
   'PYMODULE'),
  ('pip._internal.req',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_internal\\req\\__init__.py',
   'PYMODULE'),
  ('pip._internal.req.req_set',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_internal\\req\\req_set.py',
   'PYMODULE'),
  ('pip._internal.models.wheel',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_internal\\models\\wheel.py',
   'PYMODULE'),
  ('pip._internal.req.req_file',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_internal\\req\\req_file.py',
   'PYMODULE'),
  ('pip._internal.index.package_finder',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_internal\\index\\package_finder.py',
   'PYMODULE'),
  ('pip._internal.index',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_internal\\index\\__init__.py',
   'PYMODULE'),
  ('pip._internal.utils.unpacking',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_internal\\utils\\unpacking.py',
   'PYMODULE'),
  ('pip._internal.utils.filetypes',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_internal\\utils\\filetypes.py',
   'PYMODULE'),
  ('pip._internal.models.selection_prefs',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_internal\\models\\selection_prefs.py',
   'PYMODULE'),
  ('pip._internal.models.candidate',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_internal\\models\\candidate.py',
   'PYMODULE'),
  ('pip._internal.utils.models',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_internal\\utils\\models.py',
   'PYMODULE'),
  ('pip._internal.index.collector',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_internal\\index\\collector.py',
   'PYMODULE'),
  ('pip._internal.index.sources',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_internal\\index\\sources.py',
   'PYMODULE'),
  ('pip._vendor.requests.exceptions',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\requests\\exceptions.py',
   'PYMODULE'),
  ('pip._vendor.requests',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\requests\\__init__.py',
   'PYMODULE'),
  ('pip._vendor.requests.status_codes',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\requests\\status_codes.py',
   'PYMODULE'),
  ('pip._vendor.requests.structures',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\requests\\structures.py',
   'PYMODULE'),
  ('pip._vendor.requests.compat',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\requests\\compat.py',
   'PYMODULE'),
  ('pip._vendor.requests.api',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\requests\\api.py',
   'PYMODULE'),
  ('pip._vendor.requests.sessions',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\requests\\sessions.py',
   'PYMODULE'),
  ('pip._vendor.requests.adapters',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\requests\\adapters.py',
   'PYMODULE'),
  ('pip._vendor.urllib3.contrib.socks',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\urllib3\\contrib\\socks.py',
   'PYMODULE'),
  ('pip._vendor.requests._internal_utils',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\requests\\_internal_utils.py',
   'PYMODULE'),
  ('pip._vendor.requests.hooks',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\requests\\hooks.py',
   'PYMODULE'),
  ('pip._vendor.requests.cookies',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\requests\\cookies.py',
   'PYMODULE'),
  ('pip._vendor.requests.auth',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\requests\\auth.py',
   'PYMODULE'),
  ('pip._vendor.requests.packages',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\requests\\packages.py',
   'PYMODULE'),
  ('pip._vendor.requests.utils',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\requests\\utils.py',
   'PYMODULE'),
  ('pip._vendor.requests.certs',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\requests\\certs.py',
   'PYMODULE'),
  ('pip._vendor.certifi',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\certifi\\__init__.py',
   'PYMODULE'),
  ('pip._vendor.certifi.core',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\certifi\\core.py',
   'PYMODULE'),
  ('pip._vendor.requests.__version__',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\requests\\__version__.py',
   'PYMODULE'),
  ('pip._internal.utils.urls',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_internal\\utils\\urls.py',
   'PYMODULE'),
  ('pip._internal.utils.encoding',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_internal\\utils\\encoding.py',
   'PYMODULE'),
  ('pip._internal.network.utils',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_internal\\network\\utils.py',
   'PYMODULE'),
  ('pip._internal.network',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_internal\\network\\__init__.py',
   'PYMODULE'),
  ('pip._internal.network.session',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_internal\\network\\session.py',
   'PYMODULE'),
  ('pip._internal.utils.glibc',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_internal\\utils\\glibc.py',
   'PYMODULE'),
  ('pip._internal.network.cache',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_internal\\network\\cache.py',
   'PYMODULE'),
  ('pip._internal.utils.filesystem',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_internal\\utils\\filesystem.py',
   'PYMODULE'),
  ('pip._vendor.cachecontrol.caches',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\cachecontrol\\caches\\__init__.py',
   'PYMODULE'),
  ('pip._vendor.cachecontrol.caches.redis_cache',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\cachecontrol\\caches\\redis_cache.py',
   'PYMODULE'),
  ('pip._vendor.cachecontrol.caches.file_cache',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\cachecontrol\\caches\\file_cache.py',
   'PYMODULE'),
  ('pip._vendor.cachecontrol.controller',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\cachecontrol\\controller.py',
   'PYMODULE'),
  ('pip._vendor.cachecontrol.serialize',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\cachecontrol\\serialize.py',
   'PYMODULE'),
  ('pip._vendor.cachecontrol.compat',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\cachecontrol\\compat.py',
   'PYMODULE'),
  ('pip._vendor.cachecontrol.cache',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\cachecontrol\\cache.py',
   'PYMODULE'),
  ('pip._internal.network.auth',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_internal\\network\\auth.py',
   'PYMODULE'),
  ('pip._internal.vcs.versioncontrol',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_internal\\vcs\\versioncontrol.py',
   'PYMODULE'),
  ('pip._internal.utils.subprocess',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_internal\\utils\\subprocess.py',
   'PYMODULE'),
  ('pip._internal.cli.spinners',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_internal\\cli\\spinners.py',
   'PYMODULE'),
  ('pip._vendor.cachecontrol',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\cachecontrol\\__init__.py',
   'PYMODULE'),
  ('pip._vendor.cachecontrol.adapter',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\cachecontrol\\adapter.py',
   'PYMODULE'),
  ('pip._vendor.cachecontrol.filewrapper',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\cachecontrol\\filewrapper.py',
   'PYMODULE'),
  ('pip._vendor.cachecontrol.wrapper',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\cachecontrol\\wrapper.py',
   'PYMODULE'),
  ('pip._internal.models.search_scope',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_internal\\models\\search_scope.py',
   'PYMODULE'),
  ('pip._internal.vcs',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_internal\\vcs\\__init__.py',
   'PYMODULE'),
  ('pip._internal.vcs.subversion',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_internal\\vcs\\subversion.py',
   'PYMODULE'),
  ('pip._internal.vcs.mercurial',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_internal\\vcs\\mercurial.py',
   'PYMODULE'),
  ('pip._internal.vcs.git',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_internal\\vcs\\git.py',
   'PYMODULE'),
  ('pip._internal.vcs.bazaar',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_internal\\vcs\\bazaar.py',
   'PYMODULE'),
  ('pip._internal.utils.temp_dir',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_internal\\utils\\temp_dir.py',
   'PYMODULE'),
  ('pip._internal.utils.direct_url_helpers',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_internal\\utils\\direct_url_helpers.py',
   'PYMODULE'),
  ('pip._internal.req.req_uninstall',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_internal\\req\\req_uninstall.py',
   'PYMODULE'),
  ('pip._internal.pyproject',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_internal\\pyproject.py',
   'PYMODULE'),
  ('pip._vendor.tomli',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\tomli\\__init__.py',
   'PYMODULE'),
  ('pip._vendor.tomli._parser',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\tomli\\_parser.py',
   'PYMODULE'),
  ('pip._vendor.tomli._re',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\tomli\\_re.py',
   'PYMODULE'),
  ('pip._internal.operations.install.wheel',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_internal\\operations\\install\\wheel.py',
   'PYMODULE'),
  ('pip._internal.operations.install',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_internal\\operations\\install\\__init__.py',
   'PYMODULE'),
  ('pip._internal.operations',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_internal\\operations\\__init__.py',
   'PYMODULE'),
  ('pip._vendor.distlib.util',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\distlib\\util.py',
   'PYMODULE'),
  ('pip._vendor.distlib.compat',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\distlib\\compat.py',
   'PYMODULE'),
  ('logging.config',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\logging\\config.py',
   'PYMODULE'),
  ('pip._vendor.distlib._backport.sysconfig',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\distlib\\_backport\\sysconfig.py',
   'PYMODULE'),
  ('pip._vendor.distlib.resources',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\distlib\\resources.py',
   'PYMODULE'),
  ('html.parser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\html\\parser.py',
   'PYMODULE'),
  ('_markupbase',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\_markupbase.py',
   'PYMODULE'),
  ('pip._vendor.distlib._backport.shutil',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\distlib\\_backport\\shutil.py',
   'PYMODULE'),
  ('pip._vendor.distlib._backport.tarfile',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\distlib\\_backport\\tarfile.py',
   'PYMODULE'),
  ('pip._vendor.distlib._backport',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\distlib\\_backport\\__init__.py',
   'PYMODULE'),
  ('pip._vendor.distlib',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\distlib\\__init__.py',
   'PYMODULE'),
  ('pip._vendor.distlib.scripts',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\distlib\\scripts.py',
   'PYMODULE'),
  ('compileall',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\compileall.py',
   'PYMODULE'),
  ('filecmp',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\filecmp.py',
   'PYMODULE'),
  ('pip._internal.operations.install.legacy',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_internal\\operations\\install\\legacy.py',
   'PYMODULE'),
  ('pip._internal.utils.setuptools_build',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_internal\\utils\\setuptools_build.py',
   'PYMODULE'),
  ('pip._internal.operations.install.editable_legacy',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_internal\\operations\\install\\editable_legacy.py',
   'PYMODULE'),
  ('pip._internal.operations.build.metadata_legacy',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_internal\\operations\\build\\metadata_legacy.py',
   'PYMODULE'),
  ('pip._internal.operations.build',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_internal\\operations\\build\\__init__.py',
   'PYMODULE'),
  ('pip._internal.operations.build.metadata',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_internal\\operations\\build\\metadata.py',
   'PYMODULE'),
  ('pip._internal.models.link',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_internal\\models\\link.py',
   'PYMODULE'),
  ('pip._internal.build_env',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_internal\\build_env.py',
   'PYMODULE'),
  ('pip._vendor.pep517.wrappers',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\pep517\\wrappers.py',
   'PYMODULE'),
  ('pip._vendor.pep517.in_process',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\pep517\\in_process\\__init__.py',
   'PYMODULE'),
  ('pip._vendor.pep517.compat',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\pep517\\compat.py',
   'PYMODULE'),
  ('pip._vendor.pep517',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\pep517\\__init__.py',
   'PYMODULE'),
  ('pip._vendor.requests.models',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_vendor\\requests\\models.py',
   'PYMODULE'),
  ('pip._internal.commands',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_internal\\commands\\__init__.py',
   'PYMODULE'),
  ('difflib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\difflib.py',
   'PYMODULE'),
  ('pip._internal.cli.base_command',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_internal\\cli\\base_command.py',
   'PYMODULE'),
  ('pip._internal.cli.command_context',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_internal\\cli\\command_context.py',
   'PYMODULE'),
  ('pip._internal.cli.main_parser',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_internal\\cli\\main_parser.py',
   'PYMODULE'),
  ('pip._internal.cli.autocompletion',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_internal\\cli\\autocompletion.py',
   'PYMODULE'),
  ('pip._internal',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_internal\\__init__.py',
   'PYMODULE'),
  ('pip._internal.utils.inject_securetransport',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\_internal\\utils\\inject_securetransport.py',
   'PYMODULE'),
  ('pip',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pip\\__init__.py',
   'PYMODULE'),
  ('plugins.event', 'E:\\Desktop\\企业微信\\plugins\\event.py', 'PYMODULE'),
  ('common.sorted_dict',
   'E:\\Desktop\\企业微信\\common\\sorted_dict.py',
   'PYMODULE'),
  ('channel.channel_factory',
   'E:\\Desktop\\企业微信\\channel\\channel_factory.py',
   'PYMODULE'),
  ('channel', '-', 'PYMODULE'),
  ('channel.gewechat.gewechat_channel',
   'E:\\Desktop\\企业微信\\channel\\gewechat\\gewechat_channel.py',
   'PYMODULE'),
  ('channel.gewechat', '-', 'PYMODULE'),
  ('common.tmp_dir', 'E:\\Desktop\\企业微信\\common\\tmp_dir.py', 'PYMODULE'),
  ('channel.gewechat.gewechat_message',
   'E:\\Desktop\\企业微信\\channel\\gewechat\\gewechat_message.py',
   'PYMODULE'),
  ('channel.chat_message',
   'E:\\Desktop\\企业微信\\channel\\chat_message.py',
   'PYMODULE'),
  ('channel.chat_channel',
   'E:\\Desktop\\企业微信\\channel\\chat_channel.py',
   'PYMODULE'),
  ('common.dequeue', 'E:\\Desktop\\企业微信\\common\\dequeue.py', 'PYMODULE'),
  ('channel.dingtalk.dingtalk_channel',
   'E:\\Desktop\\企业微信\\channel\\dingtalk\\dingtalk_channel.py',
   'PYMODULE'),
  ('channel.dingtalk', '-', 'PYMODULE'),
  ('common.time_check', 'E:\\Desktop\\企业微信\\common\\time_check.py', 'PYMODULE'),
  ('channel.dingtalk.dingtalk_message',
   'E:\\Desktop\\企业微信\\channel\\dingtalk\\dingtalk_message.py',
   'PYMODULE'),
  ('channel.feishu.feishu_channel',
   'E:\\Desktop\\企业微信\\channel\\feishu\\feishu_channel.py',
   'PYMODULE'),
  ('channel.feishu', '-', 'PYMODULE'),
  ('channel.feishu.feishu_message',
   'E:\\Desktop\\企业微信\\channel\\feishu\\feishu_message.py',
   'PYMODULE'),
  ('channel.wework.wework_channel',
   'E:\\Desktop\\企业微信\\channel\\wework\\wework_channel.py',
   'PYMODULE'),
  ('channel.wework', '-', 'PYMODULE'),
  ('channel.wework.run',
   'E:\\Desktop\\企业微信\\channel\\wework\\run.py',
   'PYMODULE'),
  ('channel.wework.wework_message',
   'E:\\Desktop\\企业微信\\channel\\wework\\wework_message.py',
   'PYMODULE'),
  ('channel.wechatcs.wechatcomservice_channel',
   'E:\\Desktop\\企业微信\\channel\\wechatcs\\wechatcomservice_channel.py',
   'PYMODULE'),
  ('channel.wechatcs', '-', 'PYMODULE'),
  ('channel.wechatcs.wechatcomservice_message',
   'E:\\Desktop\\企业微信\\channel\\wechatcs\\wechatcomservice_message.py',
   'PYMODULE'),
  ('channel.wechatcom.wechatcomapp_client',
   'E:\\Desktop\\企业微信\\channel\\wechatcom\\wechatcomapp_client.py',
   'PYMODULE'),
  ('channel.wechatcom', '-', 'PYMODULE'),
  ('channel.wechatcom.wechatcomapp_channel',
   'E:\\Desktop\\企业微信\\channel\\wechatcom\\wechatcomapp_channel.py',
   'PYMODULE'),
  ('channel.wechatcom.wechatcomapp_message',
   'E:\\Desktop\\企业微信\\channel\\wechatcom\\wechatcomapp_message.py',
   'PYMODULE'),
  ('channel.wechatmp.wechatmp_channel',
   'E:\\Desktop\\企业微信\\channel\\wechatmp\\wechatmp_channel.py',
   'PYMODULE'),
  ('channel.wechatmp', '-', 'PYMODULE'),
  ('channel.wechatmp.wechatmp_client',
   'E:\\Desktop\\企业微信\\channel\\wechatmp\\wechatmp_client.py',
   'PYMODULE'),
  ('channel.wechatmp.common',
   'E:\\Desktop\\企业微信\\channel\\wechatmp\\common.py',
   'PYMODULE'),
  ('imghdr',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\imghdr.py',
   'PYMODULE'),
  ('channel.web.web_channel',
   'E:\\Desktop\\企业微信\\channel\\web\\web_channel.py',
   'PYMODULE'),
  ('channel.web', '-', 'PYMODULE'),
  ('channel.terminal.terminal_channel',
   'E:\\Desktop\\企业微信\\channel\\terminal\\terminal_channel.py',
   'PYMODULE'),
  ('channel.terminal', '-', 'PYMODULE'),
  ('channel.wechat.wcf_channel',
   'E:\\Desktop\\企业微信\\channel\\wechat\\wcf_channel.py',
   'PYMODULE'),
  ('channel.wechat', '-', 'PYMODULE'),
  ('channel.wechat.wcf_message',
   'E:\\Desktop\\企业微信\\channel\\wechat\\wcf_message.py',
   'PYMODULE'),
  ('channel.wechat.wechaty_channel',
   'E:\\Desktop\\企业微信\\channel\\wechat\\wechaty_channel.py',
   'PYMODULE'),
  ('channel.wechat.wechaty_message',
   'E:\\Desktop\\企业微信\\channel\\wechat\\wechaty_message.py',
   'PYMODULE'),
  ('channel.wechat.wechat_channel',
   'E:\\Desktop\\企业微信\\channel\\wechat\\wechat_channel.py',
   'PYMODULE'),
  ('common.linkai_client',
   'E:\\Desktop\\企业微信\\common\\linkai_client.py',
   'PYMODULE'),
  ('lib', '-', 'PYMODULE'),
  ('channel.wechat.wechat_message',
   'E:\\Desktop\\企业微信\\channel\\wechat\\wechat_message.py',
   'PYMODULE'),
  ('channel.channel', 'E:\\Desktop\\企业微信\\channel\\channel.py', 'PYMODULE'),
  ('bot.bot_factory', 'E:\\Desktop\\企业微信\\bot\\bot_factory.py', 'PYMODULE'),
  ('bot', '-', 'PYMODULE'),
  ('bot.mock.mock_bot',
   'E:\\Desktop\\企业微信\\bot\\mock\\mock_bot.py',
   'PYMODULE'),
  ('bot.mock', 'E:\\Desktop\\企业微信\\bot\\mock\\__init__.py', 'PYMODULE'),
  ('bot.bot', 'E:\\Desktop\\企业微信\\bot\\bot.py', 'PYMODULE'),
  ('bot.modelscope.modelscope_bot',
   'E:\\Desktop\\企业微信\\bot\\modelscope\\modelscope_bot.py',
   'PYMODULE'),
  ('bot.modelscope', '-', 'PYMODULE'),
  ('bot.modelscope.modelscope_session',
   'E:\\Desktop\\企业微信\\bot\\modelscope\\modelscope_session.py',
   'PYMODULE'),
  ('bot.session_manager',
   'E:\\Desktop\\企业微信\\bot\\session_manager.py',
   'PYMODULE'),
  ('bot.deepseek.deepseek_bot',
   'E:\\Desktop\\企业微信\\bot\\deepseek\\deepseek_bot.py',
   'PYMODULE'),
  ('bot.deepseek', 'E:\\Desktop\\企业微信\\bot\\deepseek\\__init__.py', 'PYMODULE'),
  ('bot.deepseek.deepseek_session',
   'E:\\Desktop\\企业微信\\bot\\deepseek\\deepseek_session.py',
   'PYMODULE'),
  ('bot.minimax.minimax_bot',
   'E:\\Desktop\\企业微信\\bot\\minimax\\minimax_bot.py',
   'PYMODULE'),
  ('bot.minimax', '-', 'PYMODULE'),
  ('bot.chatgpt.chat_gpt_session',
   'E:\\Desktop\\企业微信\\bot\\chatgpt\\chat_gpt_session.py',
   'PYMODULE'),
  ('bot.chatgpt', '-', 'PYMODULE'),
  ('bot.minimax.minimax_session',
   'E:\\Desktop\\企业微信\\bot\\minimax\\minimax_session.py',
   'PYMODULE'),
  ('bot.moonshot.moonshot_bot',
   'E:\\Desktop\\企业微信\\bot\\moonshot\\moonshot_bot.py',
   'PYMODULE'),
  ('bot.moonshot', '-', 'PYMODULE'),
  ('bot.moonshot.moonshot_session',
   'E:\\Desktop\\企业微信\\bot\\moonshot\\moonshot_session.py',
   'PYMODULE'),
  ('bot.bytedance.bytedance_coze_bot',
   'E:\\Desktop\\企业微信\\bot\\bytedance\\bytedance_coze_bot.py',
   'PYMODULE'),
  ('bot.bytedance', '-', 'PYMODULE'),
  ('bot.bytedance.coze_session',
   'E:\\Desktop\\企业微信\\bot\\bytedance\\coze_session.py',
   'PYMODULE'),
  ('bot.bytedance.coze_client',
   'E:\\Desktop\\企业微信\\bot\\bytedance\\coze_client.py',
   'PYMODULE'),
  ('bot.zhipuai.zhipuai_bot',
   'E:\\Desktop\\企业微信\\bot\\zhipuai\\zhipuai_bot.py',
   'PYMODULE'),
  ('bot.zhipuai', '-', 'PYMODULE'),
  ('bot.zhipuai.zhipu_ai_image',
   'E:\\Desktop\\企业微信\\bot\\zhipuai\\zhipu_ai_image.py',
   'PYMODULE'),
  ('bot.zhipuai.zhipu_ai_session',
   'E:\\Desktop\\企业微信\\bot\\zhipuai\\zhipu_ai_session.py',
   'PYMODULE'),
  ('bot.dify.dify_bot',
   'E:\\Desktop\\企业微信\\bot\\dify\\dify_bot.py',
   'PYMODULE'),
  ('bot.dify', '-', 'PYMODULE'),
  ('bot.dify.dify_session',
   'E:\\Desktop\\企业微信\\bot\\dify\\dify_session.py',
   'PYMODULE'),
  ('bot.gemini.google_gemini_bot',
   'E:\\Desktop\\企业微信\\bot\\gemini\\google_gemini_bot.py',
   'PYMODULE'),
  ('bot.gemini', '-', 'PYMODULE'),
  ('bot.baidu.baidu_wenxin_session',
   'E:\\Desktop\\企业微信\\bot\\baidu\\baidu_wenxin_session.py',
   'PYMODULE'),
  ('bot.baidu', '-', 'PYMODULE'),
  ('bot.dashscope.dashscope_bot',
   'E:\\Desktop\\企业微信\\bot\\dashscope\\dashscope_bot.py',
   'PYMODULE'),
  ('bot.dashscope', '-', 'PYMODULE'),
  ('bot.dashscope.dashscope_session',
   'E:\\Desktop\\企业微信\\bot\\dashscope\\dashscope_session.py',
   'PYMODULE'),
  ('bot.ali.ali_qwen_bot',
   'E:\\Desktop\\企业微信\\bot\\ali\\ali_qwen_bot.py',
   'PYMODULE'),
  ('bot.ali', '-', 'PYMODULE'),
  ('bot.openai.open_ai_vision',
   'E:\\Desktop\\企业微信\\bot\\openai\\open_ai_vision.py',
   'PYMODULE'),
  ('bot.openai', '-', 'PYMODULE'),
  ('bot.openai.open_ai_image',
   'E:\\Desktop\\企业微信\\bot\\openai\\open_ai_image.py',
   'PYMODULE'),
  ('common.token_bucket',
   'E:\\Desktop\\企业微信\\common\\token_bucket.py',
   'PYMODULE'),
  ('bot.ali.ali_qwen_session',
   'E:\\Desktop\\企业微信\\bot\\ali\\ali_qwen_session.py',
   'PYMODULE'),
  ('bot.claudeapi.claude_api_bot',
   'E:\\Desktop\\企业微信\\bot\\claudeapi\\claude_api_bot.py',
   'PYMODULE'),
  ('bot.claudeapi', '-', 'PYMODULE'),
  ('bot.claude.claude_ai_bot',
   'E:\\Desktop\\企业微信\\bot\\claude\\claude_ai_bot.py',
   'PYMODULE'),
  ('bot.claude', '-', 'PYMODULE'),
  ('bot.claude.claude_ai_session',
   'E:\\Desktop\\企业微信\\bot\\claude\\claude_ai_session.py',
   'PYMODULE'),
  ('bot.linkai.link_ai_bot',
   'E:\\Desktop\\企业微信\\bot\\linkai\\link_ai_bot.py',
   'PYMODULE'),
  ('bot.linkai', '-', 'PYMODULE'),
  ('bot.xunfei.xunfei_spark_bot',
   'E:\\Desktop\\企业微信\\bot\\xunfei\\xunfei_spark_bot.py',
   'PYMODULE'),
  ('bot.xunfei', '-', 'PYMODULE'),
  ('wsgiref.handlers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\wsgiref\\handlers.py',
   'PYMODULE'),
  ('wsgiref',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\wsgiref\\__init__.py',
   'PYMODULE'),
  ('wsgiref.headers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\wsgiref\\headers.py',
   'PYMODULE'),
  ('wsgiref.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\wsgiref\\util.py',
   'PYMODULE'),
  ('bot.openai.open_ai_bot',
   'E:\\Desktop\\企业微信\\bot\\openai\\open_ai_bot.py',
   'PYMODULE'),
  ('bot.openai.open_ai_session',
   'E:\\Desktop\\企业微信\\bot\\openai\\open_ai_session.py',
   'PYMODULE'),
  ('bot.chatgpt.chat_gpt_bot',
   'E:\\Desktop\\企业微信\\bot\\chatgpt\\chat_gpt_bot.py',
   'PYMODULE'),
  ('bot.baidu.baidu_wenxin',
   'E:\\Desktop\\企业微信\\bot\\baidu\\baidu_wenxin.py',
   'PYMODULE'),
  ('bridge.reply', 'E:\\Desktop\\企业微信\\bridge\\reply.py', 'PYMODULE'),
  ('bridge', '-', 'PYMODULE'),
  ('bridge.context', 'E:\\Desktop\\企业微信\\bridge\\context.py', 'PYMODULE'),
  ('bridge.bridge', 'E:\\Desktop\\企业微信\\bridge\\bridge.py', 'PYMODULE'),
  ('translate.factory', 'E:\\Desktop\\企业微信\\translate\\factory.py', 'PYMODULE'),
  ('translate', 'E:\\Desktop\\企业微信\\translate\\__init__.py', 'PYMODULE'),
  ('translate.baidu.baidu_translate',
   'E:\\Desktop\\企业微信\\translate\\baidu\\baidu_translate.py',
   'PYMODULE'),
  ('translate.baidu', '-', 'PYMODULE'),
  ('translate.translator',
   'E:\\Desktop\\企业微信\\translate\\translator.py',
   'PYMODULE'),
  ('common.singleton', 'E:\\Desktop\\企业微信\\common\\singleton.py', 'PYMODULE'),
  ('common.utils', 'E:\\Desktop\\企业微信\\common\\utils.py', 'PYMODULE'),
  ('common.log', 'E:\\Desktop\\企业微信\\common\\log.py', 'PYMODULE'),
  ('config', 'E:\\Desktop\\企业微信\\config.py', 'PYMODULE'),
  ('gui.donate_window', 'E:\\Desktop\\企业微信\\gui\\donate_window.py', 'PYMODULE'),
  ('gui', 'E:\\Desktop\\企业微信\\gui\\__init__.py', 'PYMODULE'),
  ('gui.help_window', 'E:\\Desktop\\企业微信\\gui\\help_window.py', 'PYMODULE'),
  ('gui.system_tray', 'E:\\Desktop\\企业微信\\gui\\system_tray.py', 'PYMODULE'),
  ('gui.managers', 'E:\\Desktop\\企业微信\\gui\\managers.py', 'PYMODULE'),
  ('gui.config_loader', 'E:\\Desktop\\企业微信\\gui\\config_loader.py', 'PYMODULE'),
  ('gui.controllers', 'E:\\Desktop\\企业微信\\gui\\controllers.py', 'PYMODULE'),
  ('aiohttp.web',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\aiohttp\\web.py',
   'PYMODULE'),
  ('aiohttp.web_ws',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\aiohttp\\web_ws.py',
   'PYMODULE'),
  ('async_timeout',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\async_timeout\\__init__.py',
   'PYMODULE'),
  ('aiohttp.streams',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\aiohttp\\streams.py',
   'PYMODULE'),
  ('aiohttp.base_protocol',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\aiohttp\\base_protocol.py',
   'PYMODULE'),
  ('aiohttp.tcp_helpers',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\aiohttp\\tcp_helpers.py',
   'PYMODULE'),
  ('aiohttp.http_websocket',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\aiohttp\\http_websocket.py',
   'PYMODULE'),
  ('aiohttp._websocket.models',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\aiohttp\\_websocket\\models.py',
   'PYMODULE'),
  ('aiohttp._websocket',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\aiohttp\\_websocket\\__init__.py',
   'PYMODULE'),
  ('aiohttp._websocket.helpers',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\aiohttp\\_websocket\\helpers.py',
   'PYMODULE'),
  ('aiohttp.http',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\aiohttp\\http.py',
   'PYMODULE'),
  ('aiohttp.http_writer',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\aiohttp\\http_writer.py',
   'PYMODULE'),
  ('aiohttp.compression_utils',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\aiohttp\\compression_utils.py',
   'PYMODULE'),
  ('aiohttp.http_parser',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\aiohttp\\http_parser.py',
   'PYMODULE'),
  ('yarl',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\yarl\\__init__.py',
   'PYMODULE'),
  ('yarl._url',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\yarl\\_url.py',
   'PYMODULE'),
  ('yarl._quoters',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\yarl\\_quoters.py',
   'PYMODULE'),
  ('yarl._quoting',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\yarl\\_quoting.py',
   'PYMODULE'),
  ('yarl._quoting_py',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\yarl\\_quoting_py.py',
   'PYMODULE'),
  ('yarl._path',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\yarl\\_path.py',
   'PYMODULE'),
  ('yarl._parse',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\yarl\\_parse.py',
   'PYMODULE'),
  ('propcache.api',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\propcache\\api.py',
   'PYMODULE'),
  ('propcache',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\propcache\\__init__.py',
   'PYMODULE'),
  ('propcache._helpers',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\propcache\\_helpers.py',
   'PYMODULE'),
  ('propcache._helpers_py',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\propcache\\_helpers_py.py',
   'PYMODULE'),
  ('yarl._query',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\yarl\\_query.py',
   'PYMODULE'),
  ('aiohttp.http_exceptions',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\aiohttp\\http_exceptions.py',
   'PYMODULE'),
  ('aiohttp.client_exceptions',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\aiohttp\\client_exceptions.py',
   'PYMODULE'),
  ('aiohttp.client_reqrep',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\aiohttp\\client_reqrep.py',
   'PYMODULE'),
  ('aiohttp.tracing',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\aiohttp\\tracing.py',
   'PYMODULE'),
  ('aiosignal',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\aiosignal\\__init__.py',
   'PYMODULE'),
  ('frozenlist',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\frozenlist\\__init__.py',
   'PYMODULE'),
  ('aiohttp.connector',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\aiohttp\\connector.py',
   'PYMODULE'),
  ('aiohttp.resolver',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\aiohttp\\resolver.py',
   'PYMODULE'),
  ('aiohttp.client_proto',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\aiohttp\\client_proto.py',
   'PYMODULE'),
  ('aiohappyeyeballs',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\aiohappyeyeballs\\__init__.py',
   'PYMODULE'),
  ('aiohappyeyeballs.utils',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\aiohappyeyeballs\\utils.py',
   'PYMODULE'),
  ('aiohappyeyeballs.types',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\aiohappyeyeballs\\types.py',
   'PYMODULE'),
  ('aiohappyeyeballs.impl',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\aiohappyeyeballs\\impl.py',
   'PYMODULE'),
  ('aiohappyeyeballs._staggered',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\aiohappyeyeballs\\_staggered.py',
   'PYMODULE'),
  ('aiohttp.formdata',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\aiohttp\\formdata.py',
   'PYMODULE'),
  ('aiohttp._cookie_helpers',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\aiohttp\\_cookie_helpers.py',
   'PYMODULE'),
  ('aiohttp.payload',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\aiohttp\\payload.py',
   'PYMODULE'),
  ('aiohttp.multipart',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\aiohttp\\multipart.py',
   'PYMODULE'),
  ('aiohttp._websocket.writer',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\aiohttp\\_websocket\\writer.py',
   'PYMODULE'),
  ('aiohttp._websocket.reader',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\aiohttp\\_websocket\\reader.py',
   'PYMODULE'),
  ('aiohttp._websocket.reader_py',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\aiohttp\\_websocket\\reader_py.py',
   'PYMODULE'),
  ('aiohttp.hdrs',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\aiohttp\\hdrs.py',
   'PYMODULE'),
  ('multidict',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\multidict\\__init__.py',
   'PYMODULE'),
  ('multidict._multidict_py',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\multidict\\_multidict_py.py',
   'PYMODULE'),
  ('multidict._compat',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\multidict\\_compat.py',
   'PYMODULE'),
  ('multidict._abc',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\multidict\\_abc.py',
   'PYMODULE'),
  ('attr',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\attr\\__init__.py',
   'PYMODULE'),
  ('attr._version_info',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\attr\\_version_info.py',
   'PYMODULE'),
  ('attr._next_gen',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\attr\\_next_gen.py',
   'PYMODULE'),
  ('attr._make',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\attr\\_make.py',
   'PYMODULE'),
  ('attr._compat',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\attr\\_compat.py',
   'PYMODULE'),
  ('attr._funcs',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\attr\\_funcs.py',
   'PYMODULE'),
  ('attr._cmp',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\attr\\_cmp.py',
   'PYMODULE'),
  ('attr.validators',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\attr\\validators.py',
   'PYMODULE'),
  ('attr.filters',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\attr\\filters.py',
   'PYMODULE'),
  ('attr.exceptions',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\attr\\exceptions.py',
   'PYMODULE'),
  ('attr.converters',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\attr\\converters.py',
   'PYMODULE'),
  ('attr.setters',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\attr\\setters.py',
   'PYMODULE'),
  ('attr._config',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\attr\\_config.py',
   'PYMODULE'),
  ('aiohttp.web_urldispatcher',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\aiohttp\\web_urldispatcher.py',
   'PYMODULE'),
  ('aiohttp.web_server',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\aiohttp\\web_server.py',
   'PYMODULE'),
  ('aiohttp.web_runner',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\aiohttp\\web_runner.py',
   'PYMODULE'),
  ('aiohttp.web_routedef',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\aiohttp\\web_routedef.py',
   'PYMODULE'),
  ('aiohttp.web_response',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\aiohttp\\web_response.py',
   'PYMODULE'),
  ('aiohttp.web_request',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\aiohttp\\web_request.py',
   'PYMODULE'),
  ('aiohttp.web_protocol',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\aiohttp\\web_protocol.py',
   'PYMODULE'),
  ('aiohttp.web_middlewares',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\aiohttp\\web_middlewares.py',
   'PYMODULE'),
  ('aiohttp.web_log',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\aiohttp\\web_log.py',
   'PYMODULE'),
  ('aiohttp.web_fileresponse',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\aiohttp\\web_fileresponse.py',
   'PYMODULE'),
  ('aiohttp.web_exceptions',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\aiohttp\\web_exceptions.py',
   'PYMODULE'),
  ('aiohttp.web_app',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\aiohttp\\web_app.py',
   'PYMODULE'),
  ('aiohttp.typedefs',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\aiohttp\\typedefs.py',
   'PYMODULE'),
  ('aiohttp.log',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\aiohttp\\log.py',
   'PYMODULE'),
  ('aiohttp.helpers',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\aiohttp\\helpers.py',
   'PYMODULE'),
  ('aiohttp.abc',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\aiohttp\\abc.py',
   'PYMODULE'),
  ('aiohttp.client',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\aiohttp\\client.py',
   'PYMODULE'),
  ('aiohttp.cookiejar',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\aiohttp\\cookiejar.py',
   'PYMODULE'),
  ('aiohttp.client_ws',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\aiohttp\\client_ws.py',
   'PYMODULE'),
  ('aiohttp.client_middlewares',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\aiohttp\\client_middlewares.py',
   'PYMODULE'),
  ('aiohttp',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\aiohttp\\__init__.py',
   'PYMODULE'),
  ('aiohttp.worker',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\aiohttp\\worker.py',
   'PYMODULE'),
  ('aiohttp.payload_streamer',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\aiohttp\\payload_streamer.py',
   'PYMODULE'),
  ('aiohttp.client_middleware_digest_auth',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\aiohttp\\client_middleware_digest_auth.py',
   'PYMODULE'),
  ('websockets.server',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\websockets\\server.py',
   'PYMODULE'),
  ('websockets.utils',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\websockets\\utils.py',
   'PYMODULE'),
  ('websockets.typing',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\websockets\\typing.py',
   'PYMODULE'),
  ('websockets.protocol',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\websockets\\protocol.py',
   'PYMODULE'),
  ('websockets.streams',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\websockets\\streams.py',
   'PYMODULE'),
  ('websockets.frames',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\websockets\\frames.py',
   'PYMODULE'),
  ('websockets.imports',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\websockets\\imports.py',
   'PYMODULE'),
  ('websockets.http11',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\websockets\\http11.py',
   'PYMODULE'),
  ('websockets.version',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\websockets\\version.py',
   'PYMODULE'),
  ('websockets.headers',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\websockets\\headers.py',
   'PYMODULE'),
  ('websockets.extensions',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\websockets\\extensions\\__init__.py',
   'PYMODULE'),
  ('websockets.extensions.base',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\websockets\\extensions\\base.py',
   'PYMODULE'),
  ('websockets.exceptions',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\websockets\\exceptions.py',
   'PYMODULE'),
  ('websockets.datastructures',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\websockets\\datastructures.py',
   'PYMODULE'),
  ('websockets.client',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\websockets\\client.py',
   'PYMODULE'),
  ('websockets.uri',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\websockets\\uri.py',
   'PYMODULE'),
  ('websockets',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\websockets\\__init__.py',
   'PYMODULE'),
  ('websockets.sync.utils',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\websockets\\sync\\utils.py',
   'PYMODULE'),
  ('websockets.sync.server',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\websockets\\sync\\server.py',
   'PYMODULE'),
  ('websockets.sync.router',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\websockets\\sync\\router.py',
   'PYMODULE'),
  ('websockets.sync.messages',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\websockets\\sync\\messages.py',
   'PYMODULE'),
  ('websockets.sync.connection',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\websockets\\sync\\connection.py',
   'PYMODULE'),
  ('websockets.sync.client',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\websockets\\sync\\client.py',
   'PYMODULE'),
  ('websockets.sync',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\websockets\\sync\\__init__.py',
   'PYMODULE'),
  ('websockets.legacy.server',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\websockets\\legacy\\server.py',
   'PYMODULE'),
  ('websockets.legacy.protocol',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\websockets\\legacy\\protocol.py',
   'PYMODULE'),
  ('websockets.legacy.http',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\websockets\\legacy\\http.py',
   'PYMODULE'),
  ('websockets.legacy.handshake',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\websockets\\legacy\\handshake.py',
   'PYMODULE'),
  ('websockets.legacy.framing',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\websockets\\legacy\\framing.py',
   'PYMODULE'),
  ('websockets.legacy.exceptions',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\websockets\\legacy\\exceptions.py',
   'PYMODULE'),
  ('websockets.legacy.client',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\websockets\\legacy\\client.py',
   'PYMODULE'),
  ('websockets.legacy.auth',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\websockets\\legacy\\auth.py',
   'PYMODULE'),
  ('websockets.legacy',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\websockets\\legacy\\__init__.py',
   'PYMODULE'),
  ('websockets.http',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\websockets\\http.py',
   'PYMODULE'),
  ('websockets.extensions.permessage_deflate',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\websockets\\extensions\\permessage_deflate.py',
   'PYMODULE'),
  ('websockets.connection',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\websockets\\connection.py',
   'PYMODULE'),
  ('websockets.cli',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\websockets\\cli.py',
   'PYMODULE'),
  ('websockets.auth',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\websockets\\auth.py',
   'PYMODULE'),
  ('websockets.asyncio.messages',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\websockets\\asyncio\\messages.py',
   'PYMODULE'),
  ('websockets.asyncio.connection',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\websockets\\asyncio\\connection.py',
   'PYMODULE'),
  ('websockets.asyncio.compatibility',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\websockets\\asyncio\\compatibility.py',
   'PYMODULE'),
  ('websockets.asyncio.async_timeout',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\websockets\\asyncio\\async_timeout.py',
   'PYMODULE'),
  ('websockets.asyncio',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\websockets\\asyncio\\__init__.py',
   'PYMODULE'),
  ('websockets.__main__',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\websockets\\__main__.py',
   'PYMODULE'),
  ('websockets.asyncio.server',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\websockets\\asyncio\\server.py',
   'PYMODULE'),
  ('websockets.asyncio.router',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\websockets\\asyncio\\router.py',
   'PYMODULE'),
  ('websockets.asyncio.client',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\websockets\\asyncio\\client.py',
   'PYMODULE'),
  ('pyee.asyncio',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pyee\\asyncio.py',
   'PYMODULE'),
  ('pyee.base',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pyee\\base.py',
   'PYMODULE'),
  ('pyee',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pyee\\__init__.py',
   'PYMODULE'),
  ('pilk',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pilk\\__init__.py',
   'PYMODULE'),
  ('pilk.SilkEncoder',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pilk\\SilkEncoder.py',
   'PYMODULE'),
  ('pilk.SilkDecoder',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pilk\\SilkDecoder.py',
   'PYMODULE'),
  ('wave',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\wave.py',
   'PYMODULE'),
  ('chunk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\chunk.py',
   'PYMODULE'),
  ('ntwork.wc', 'E:\\Desktop\\企业微信\\ntwork\\wc\\__init__.py', 'PYMODULE'),
  ('ntwork.exception',
   'E:\\Desktop\\企业微信\\ntwork\\exception\\__init__.py',
   'PYMODULE'),
  ('ntwork.const.send_type',
   'E:\\Desktop\\企业微信\\ntwork\\const\\send_type.py',
   'PYMODULE'),
  ('ntwork.const.notify_type',
   'E:\\Desktop\\企业微信\\ntwork\\const\\notify_type.py',
   'PYMODULE'),
  ('ntwork.const', 'E:\\Desktop\\企业微信\\ntwork\\const\\__init__.py', 'PYMODULE'),
  ('ntwork.utils.xdg', 'E:\\Desktop\\企业微信\\ntwork\\utils\\xdg.py', 'PYMODULE'),
  ('ntwork.utils.singleton',
   'E:\\Desktop\\企业微信\\ntwork\\utils\\singleton.py',
   'PYMODULE'),
  ('ntwork.utils.logger',
   'E:\\Desktop\\企业微信\\ntwork\\utils\\logger.py',
   'PYMODULE'),
  ('ntwork.conf', 'E:\\Desktop\\企业微信\\ntwork\\conf\\__init__.py', 'PYMODULE'),
  ('ntwork.utils', 'E:\\Desktop\\企业微信\\ntwork\\utils\\__init__.py', 'PYMODULE'),
  ('ntwork.core.mgr', 'E:\\Desktop\\企业微信\\ntwork\\core\\mgr.py', 'PYMODULE'),
  ('ntwork.core.wework',
   'E:\\Desktop\\企业微信\\ntwork\\core\\wework.py',
   'PYMODULE'),
  ('ntwork.core', 'E:\\Desktop\\企业微信\\ntwork\\core\\__init__.py', 'PYMODULE'),
  ('ntwork', 'E:\\Desktop\\企业微信\\ntwork\\__init__.py', 'PYMODULE'),
  ('PIL._tkinter_finder',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\PIL\\_tkinter_finder.py',
   'PYMODULE'),
  ('PIL.ImageTransform',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\PIL\\ImageTransform.py',
   'PYMODULE'),
  ('PIL.ImagePalette',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\PIL\\ImagePalette.py',
   'PYMODULE'),
  ('PIL.PaletteFile',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\PIL\\PaletteFile.py',
   'PYMODULE'),
  ('PIL._binary',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\PIL\\_binary.py',
   'PYMODULE'),
  ('PIL.GimpPaletteFile',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\PIL\\GimpPaletteFile.py',
   'PYMODULE'),
  ('PIL.GimpGradientFile',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\PIL\\GimpGradientFile.py',
   'PYMODULE'),
  ('PIL.ImageMode',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\PIL\\ImageMode.py',
   'PYMODULE'),
  ('PIL._deprecate',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\PIL\\_deprecate.py',
   'PYMODULE'),
  ('PIL.ImageMath',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\PIL\\ImageMath.py',
   'PYMODULE'),
  ('PIL.ImageCms',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\PIL\\ImageCms.py',
   'PYMODULE'),
  ('PIL._util',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\PIL\\_util.py',
   'PYMODULE'),
  ('PIL._typing',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\PIL\\_typing.py',
   'PYMODULE'),
  ('numpy.typing',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\typing\\__init__.py',
   'PYMODULE'),
  ('numpy',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\__init__.py',
   'PYMODULE'),
  ('numpy._core._dtype_ctypes',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\_core\\_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy.strings',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\strings\\__init__.py',
   'PYMODULE'),
  ('numpy._core.strings',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\_core\\strings.py',
   'PYMODULE'),
  ('numpy._core.umath',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\_core\\umath.py',
   'PYMODULE'),
  ('numpy._core.overrides',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\_core\\overrides.py',
   'PYMODULE'),
  ('numpy._utils._inspect',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\_utils\\_inspect.py',
   'PYMODULE'),
  ('numpy._utils',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\_utils\\__init__.py',
   'PYMODULE'),
  ('numpy._utils._convertions',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\_utils\\_convertions.py',
   'PYMODULE'),
  ('numpy._core.multiarray',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\_core\\multiarray.py',
   'PYMODULE'),
  ('numpy.core',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE'),
  ('numpy.core._utils',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\core\\_utils.py',
   'PYMODULE'),
  ('numpy.char',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\char\\__init__.py',
   'PYMODULE'),
  ('numpy._core.defchararray',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\_core\\defchararray.py',
   'PYMODULE'),
  ('numpy._core.numeric',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\_core\\numeric.py',
   'PYMODULE'),
  ('numpy._core._asarray',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\_core\\_asarray.py',
   'PYMODULE'),
  ('numpy._core.arrayprint',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\_core\\arrayprint.py',
   'PYMODULE'),
  ('numpy._core.fromnumeric',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\_core\\fromnumeric.py',
   'PYMODULE'),
  ('numpy._core._methods',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\_core\\_methods.py',
   'PYMODULE'),
  ('numpy._core._exceptions',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\_core\\_exceptions.py',
   'PYMODULE'),
  ('numpy._core._ufunc_config',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\_core\\_ufunc_config.py',
   'PYMODULE'),
  ('numpy._core.shape_base',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\_core\\shape_base.py',
   'PYMODULE'),
  ('numpy._core.numerictypes',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\_core\\numerictypes.py',
   'PYMODULE'),
  ('numpy._core._dtype',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\_core\\_dtype.py',
   'PYMODULE'),
  ('numpy._core._type_aliases',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\_core\\_type_aliases.py',
   'PYMODULE'),
  ('numpy._core._string_helpers',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\_core\\_string_helpers.py',
   'PYMODULE'),
  ('numpy.rec',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\rec\\__init__.py',
   'PYMODULE'),
  ('numpy._core.records',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\_core\\records.py',
   'PYMODULE'),
  ('numpy.f2py',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\f2py\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py.diagnose',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\f2py\\diagnose.py',
   'PYMODULE'),
  ('numpy.f2py.f2py2e',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\f2py\\f2py2e.py',
   'PYMODULE'),
  ('numpy.f2py._backends',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\f2py\\_backends\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py._backends._distutils',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\f2py\\_backends\\_distutils.py',
   'PYMODULE'),
  ('numpy.f2py._backends._backend',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\f2py\\_backends\\_backend.py',
   'PYMODULE'),
  ('numpy.f2py._backends._meson',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\f2py\\_backends\\_meson.py',
   'PYMODULE'),
  ('numpy.f2py.auxfuncs',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\f2py\\auxfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.f90mod_rules',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\f2py\\f90mod_rules.py',
   'PYMODULE'),
  ('numpy.f2py.rules',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\f2py\\rules.py',
   'PYMODULE'),
  ('numpy.f2py.use_rules',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\f2py\\use_rules.py',
   'PYMODULE'),
  ('numpy.f2py.common_rules',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\f2py\\common_rules.py',
   'PYMODULE'),
  ('numpy.f2py.func2subr',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\f2py\\func2subr.py',
   'PYMODULE'),
  ('numpy.f2py._isocbind',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\f2py\\_isocbind.py',
   'PYMODULE'),
  ('numpy.f2py.crackfortran',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\f2py\\crackfortran.py',
   'PYMODULE'),
  ('fileinput',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\fileinput.py',
   'PYMODULE'),
  ('numpy.f2py.symbolic',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\f2py\\symbolic.py',
   'PYMODULE'),
  ('numpy.f2py.cb_rules',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\f2py\\cb_rules.py',
   'PYMODULE'),
  ('numpy.f2py.capi_maps',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\f2py\\capi_maps.py',
   'PYMODULE'),
  ('numpy.f2py.cfuncs',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\f2py\\cfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.__version__',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\f2py\\__version__.py',
   'PYMODULE'),
  ('numpy.matlib',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\matlib.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE'),
  ('numpy.testing',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\testing\\__init__.py',
   'PYMODULE'),
  ('numpy.testing.overrides',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\testing\\overrides.py',
   'PYMODULE'),
  ('numpy.lib.recfunctions',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\lib\\recfunctions.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE'),
  ('numpy.testing._private.extbuild',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\testing\\_private\\extbuild.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\testing\\_private\\utils.py',
   'PYMODULE'),
  ('doctest',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\doctest.py',
   'PYMODULE'),
  ('numpy._core.tests._natype',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\_core\\tests\\_natype.py',
   'PYMODULE'),
  ('numpy._core.tests', '-', 'PYMODULE'),
  ('unittest.case',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\unittest\\case.py',
   'PYMODULE'),
  ('unittest._log',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\unittest\\_log.py',
   'PYMODULE'),
  ('unittest.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\unittest\\util.py',
   'PYMODULE'),
  ('unittest.result',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\unittest\\result.py',
   'PYMODULE'),
  ('numpy.testing._private',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\testing\\_private\\__init__.py',
   'PYMODULE'),
  ('unittest',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\unittest\\__init__.py',
   'PYMODULE'),
  ('unittest.async_case',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.signals',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\unittest\\signals.py',
   'PYMODULE'),
  ('unittest.main',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\unittest\\main.py',
   'PYMODULE'),
  ('unittest.runner',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\unittest\\runner.py',
   'PYMODULE'),
  ('unittest.loader',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\unittest\\loader.py',
   'PYMODULE'),
  ('unittest.suite',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\unittest\\suite.py',
   'PYMODULE'),
  ('numpy.exceptions',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\exceptions.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\ctypeslib.py',
   'PYMODULE'),
  ('numpy._core._internal',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\_core\\_internal.py',
   'PYMODULE'),
  ('numpy.ma',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE'),
  ('numpy.lib.array_utils',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\lib\\array_utils.py',
   'PYMODULE'),
  ('numpy.lib._array_utils_impl',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\lib\\_array_utils_impl.py',
   'PYMODULE'),
  ('numpy.ma.core',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE'),
  ('numpy.polynomial',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE'),
  ('numpy.random',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE'),
  ('numpy.dtypes',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\dtypes.py',
   'PYMODULE'),
  ('numpy.fft',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE'),
  ('numpy.fft._helper',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\fft\\_helper.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE'),
  ('numpy.linalg',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE'),
  ('numpy.linalg._linalg',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\linalg\\_linalg.py',
   'PYMODULE'),
  ('numpy._array_api_info',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\_array_api_info.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib._index_tricks_impl',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\lib\\_index_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE'),
  ('numpy.lib._npyio_impl',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\lib\\_npyio_impl.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE'),
  ('numpy.lib.format',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE'),
  ('numpy.lib._polynomial_impl',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\lib\\_polynomial_impl.py',
   'PYMODULE'),
  ('numpy.lib._stride_tricks_impl',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\lib\\_stride_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib._utils_impl',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\lib\\_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraypad_impl',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\lib\\_arraypad_impl.py',
   'PYMODULE'),
  ('numpy.lib._ufunclike_impl',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\lib\\_ufunclike_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraysetops_impl',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\lib\\_arraysetops_impl.py',
   'PYMODULE'),
  ('numpy.lib._type_check_impl',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\lib\\_type_check_impl.py',
   'PYMODULE'),
  ('numpy._core.getlimits',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\_core\\getlimits.py',
   'PYMODULE'),
  ('numpy._core._machar',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\_core\\_machar.py',
   'PYMODULE'),
  ('numpy.lib._shape_base_impl',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\lib\\_shape_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._twodim_base_impl',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\lib\\_twodim_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._function_base_impl',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\lib\\_function_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._nanfunctions_impl',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\lib\\_nanfunctions_impl.py',
   'PYMODULE'),
  ('numpy.lib._histograms_impl',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\lib\\_histograms_impl.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE'),
  ('numpy.lib._scimath_impl',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\lib\\_scimath_impl.py',
   'PYMODULE'),
  ('numpy.lib',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE'),
  ('numpy._core.function_base',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\_core\\function_base.py',
   'PYMODULE'),
  ('numpy.lib._version',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE'),
  ('numpy.lib._arrayterator_impl',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\lib\\_arrayterator_impl.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE'),
  ('numpy.lib.introspect',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\lib\\introspect.py',
   'PYMODULE'),
  ('numpy._core.printoptions',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\_core\\printoptions.py',
   'PYMODULE'),
  ('numpy._core.memmap',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\_core\\memmap.py',
   'PYMODULE'),
  ('numpy._core',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\_core\\__init__.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs_scalars',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\_core\\_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\_core\\_add_newdocs.py',
   'PYMODULE'),
  ('numpy._core.einsumfunc',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\_core\\einsumfunc.py',
   'PYMODULE'),
  ('numpy.__config__',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\__config__.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE'),
  ('numpy.version',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\version.py',
   'PYMODULE'),
  ('numpy._expired_attrs_2_0',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\_expired_attrs_2_0.py',
   'PYMODULE'),
  ('numpy._globals',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\_globals.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE'),
  ('numpy._typing._add_docstring',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\_typing\\_add_docstring.py',
   'PYMODULE'),
  ('numpy._typing._array_like',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\_typing\\_array_like.py',
   'PYMODULE'),
  ('numpy._typing._shape',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\_typing\\_shape.py',
   'PYMODULE'),
  ('numpy._typing._nested_sequence',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\_typing\\_nested_sequence.py',
   'PYMODULE'),
  ('numpy._typing._nbit_base',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\_typing\\_nbit_base.py',
   'PYMODULE'),
  ('numpy._typing',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\_typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._ufunc',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\_typing\\_ufunc.py',
   'PYMODULE'),
  ('numpy._typing._dtype_like',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\_typing\\_dtype_like.py',
   'PYMODULE'),
  ('numpy._typing._scalars',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\_typing\\_scalars.py',
   'PYMODULE'),
  ('numpy._typing._char_codes',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\_typing\\_char_codes.py',
   'PYMODULE'),
  ('numpy._typing._nbit',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\_typing\\_nbit.py',
   'PYMODULE'),
  ('PIL.ImageQt',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\PIL\\ImageQt.py',
   'PYMODULE'),
  ('PIL.ImagePath',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\PIL\\ImagePath.py',
   'PYMODULE'),
  ('PIL.ImageGrab',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\PIL\\ImageGrab.py',
   'PYMODULE'),
  ('PIL.BmpImagePlugin',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\PIL\\BmpImagePlugin.py',
   'PYMODULE'),
  ('PIL.PngImagePlugin',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\PIL\\PngImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageWin',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\PIL\\ImageWin.py',
   'PYMODULE'),
  ('PIL.ImageShow',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\PIL\\ImageShow.py',
   'PYMODULE'),
  ('PIL.ImageSequence',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\PIL\\ImageSequence.py',
   'PYMODULE'),
  ('PIL.ImageFile',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\PIL\\ImageFile.py',
   'PYMODULE'),
  ('PIL.TiffImagePlugin',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\PIL\\TiffImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffTags',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\PIL\\TiffTags.py',
   'PYMODULE'),
  ('PIL.ExifTags',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\PIL\\ExifTags.py',
   'PYMODULE'),
  ('PIL.ImageColor',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\PIL\\ImageColor.py',
   'PYMODULE'),
  ('colorsys',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\colorsys.py',
   'PYMODULE'),
  ('PIL.ImageStat',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\PIL\\ImageStat.py',
   'PYMODULE'),
  ('PIL.ImageChops',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\PIL\\ImageChops.py',
   'PYMODULE'),
  ('PIL.ImageOps',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\PIL\\ImageOps.py',
   'PYMODULE'),
  ('PIL.ImageEnhance',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\PIL\\ImageEnhance.py',
   'PYMODULE'),
  ('PIL.ImageFilter',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\PIL\\ImageFilter.py',
   'PYMODULE'),
  ('PIL.ImageFont',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\PIL\\ImageFont.py',
   'PYMODULE'),
  ('PIL.features',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\PIL\\features.py',
   'PYMODULE'),
  ('PIL.ImageDraw',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\PIL\\ImageDraw.py',
   'PYMODULE'),
  ('PIL.ImageDraw2',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\PIL\\ImageDraw2.py',
   'PYMODULE'),
  ('PIL.ImageTk',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\PIL\\ImageTk.py',
   'PYMODULE'),
  ('PIL.Image',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\PIL\\Image.py',
   'PYMODULE'),
  ('PIL.XpmImagePlugin',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\PIL\\XpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XbmImagePlugin',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\PIL\\XbmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XVThumbImagePlugin',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\PIL\\XVThumbImagePlugin.py',
   'PYMODULE'),
  ('PIL.WmfImagePlugin',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\PIL\\WmfImagePlugin.py',
   'PYMODULE'),
  ('PIL.WebPImagePlugin',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\PIL\\WebPImagePlugin.py',
   'PYMODULE'),
  ('PIL.TgaImagePlugin',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\PIL\\TgaImagePlugin.py',
   'PYMODULE'),
  ('PIL.SunImagePlugin',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\PIL\\SunImagePlugin.py',
   'PYMODULE'),
  ('PIL.SpiderImagePlugin',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\PIL\\SpiderImagePlugin.py',
   'PYMODULE'),
  ('PIL.SgiImagePlugin',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\PIL\\SgiImagePlugin.py',
   'PYMODULE'),
  ('PIL.QoiImagePlugin',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\PIL\\QoiImagePlugin.py',
   'PYMODULE'),
  ('PIL.PsdImagePlugin',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\PIL\\PsdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PixarImagePlugin',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\PIL\\PixarImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfImagePlugin',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\PIL\\PdfImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfParser',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\PIL\\PdfParser.py',
   'PYMODULE'),
  ('PIL.PcxImagePlugin',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\PIL\\PcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcdImagePlugin',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\PIL\\PcdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PalmImagePlugin',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\PIL\\PalmImagePlugin.py',
   'PYMODULE'),
  ('PIL.MspImagePlugin',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\PIL\\MspImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpoImagePlugin',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\PIL\\MpoImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpegImagePlugin',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\PIL\\MpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.MicImagePlugin',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\PIL\\MicImagePlugin.py',
   'PYMODULE'),
  ('PIL.McIdasImagePlugin',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\PIL\\McIdasImagePlugin.py',
   'PYMODULE'),
  ('PIL.Jpeg2KImagePlugin',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\PIL\\Jpeg2KImagePlugin.py',
   'PYMODULE'),
  ('PIL.IptcImagePlugin',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\PIL\\IptcImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImtImagePlugin',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\PIL\\ImtImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImImagePlugin',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\PIL\\ImImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcoImagePlugin',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\PIL\\IcoImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcnsImagePlugin',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\PIL\\IcnsImagePlugin.py',
   'PYMODULE'),
  ('PIL.Hdf5StubImagePlugin',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\PIL\\Hdf5StubImagePlugin.py',
   'PYMODULE'),
  ('PIL.GribStubImagePlugin',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\PIL\\GribStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.GbrImagePlugin',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\PIL\\GbrImagePlugin.py',
   'PYMODULE'),
  ('PIL.FtexImagePlugin',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\PIL\\FtexImagePlugin.py',
   'PYMODULE'),
  ('PIL.FpxImagePlugin',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\PIL\\FpxImagePlugin.py',
   'PYMODULE'),
  ('PIL.FliImagePlugin',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\PIL\\FliImagePlugin.py',
   'PYMODULE'),
  ('PIL.FitsImagePlugin',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\PIL\\FitsImagePlugin.py',
   'PYMODULE'),
  ('PIL.EpsImagePlugin',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\PIL\\EpsImagePlugin.py',
   'PYMODULE'),
  ('PIL.DdsImagePlugin',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\PIL\\DdsImagePlugin.py',
   'PYMODULE'),
  ('PIL.DcxImagePlugin',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\PIL\\DcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.CurImagePlugin',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\PIL\\CurImagePlugin.py',
   'PYMODULE'),
  ('PIL.BufrStubImagePlugin',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\PIL\\BufrStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.BlpImagePlugin',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\PIL\\BlpImagePlugin.py',
   'PYMODULE'),
  ('PIL.AvifImagePlugin',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\PIL\\AvifImagePlugin.py',
   'PYMODULE'),
  ('PIL.PpmImagePlugin',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\PIL\\PpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegImagePlugin',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\PIL\\JpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegPresets',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\PIL\\JpegPresets.py',
   'PYMODULE'),
  ('PIL.GifImagePlugin',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\PIL\\GifImagePlugin.py',
   'PYMODULE'),
  ('PIL',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\PIL\\__init__.py',
   'PYMODULE'),
  ('PIL._version',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\PIL\\_version.py',
   'PYMODULE'),
  ('PyQt5',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\PyQt5\\__init__.py',
   'PYMODULE'),
  ('tracemalloc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\tracemalloc.py',
   'PYMODULE'),
  ('nturl2path',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\nturl2path.py',
   'PYMODULE'),
  ('ftplib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\ftplib.py',
   'PYMODULE'),
  ('_py_abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\_py_abc.py',
   'PYMODULE'),
  ('stringprep',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\stringprep.py',
   'PYMODULE'),
  ('gui.main_window', 'E:\\Desktop\\企业微信\\gui\\main_window.py', 'PYMODULE')])
