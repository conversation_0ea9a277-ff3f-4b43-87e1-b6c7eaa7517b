#!/usr/bin/env python3
# encoding:utf-8
"""
最终图片显示修复方案
基于调试信息的针对性修复
"""

import os
import sys
import shutil
from pathlib import Path

def fix_help_window_image_loading():
    """修复帮助窗口的图片加载逻辑"""
    print("🔧 修复帮助窗口图片加载逻辑...")
    
    help_window_file = Path("gui/help_window.py")
    if not help_window_file.exists():
        print("❌ gui/help_window.py 不存在")
        return False
    
    # 读取文件
    with open(help_window_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 找到图片加载的部分并完全替换
    old_image_loading = '''        # 尝试加载图片 - 修复版
        try:
            import os
            image_path = get_contact_image_path()
            print(f"尝试加载联系方式图片: {image_path}")
            
            if os.path.exists(image_path):
                pixmap = QPixmap(image_path)
                if not pixmap.isNull():
                    # 缩放图片到合适大小
                    scaled_pixmap = pixmap.scaled(300, 300, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                    image_label.setPixmap(scaled_pixmap)
                else:
                    image_label.setText("📱 联系方式图片\\n(12.jpg)")
                    image_label.setStyleSheet(image_label.styleSheet() + "font-size: 14pt; color: #7f8c8d;")
            else:
                image_label.setText("📱 联系方式图片\\n(12.jpg 文件未找到)")
                image_label.setStyleSheet(image_label.styleSheet() + "font-size: 14pt; color: #e74c3c;")
        except Exception as e:
            image_label.setText(f"📱 联系方式图片\\n(加载失败: {str(e)})")
            image_label.setStyleSheet(image_label.styleSheet() + "font-size: 12pt; color: #e74c3c;")'''
    
    new_image_loading = '''        # 尝试加载图片 - 最终修复版
        try:
            import os
            from PyQt5.QtCore import QTimer
            
            image_path = get_contact_image_path()
            print(f"🔍 尝试加载联系方式图片: {image_path}")
            print(f"🔍 文件是否存在: {os.path.exists(image_path)}")
            
            if os.path.exists(image_path):
                print(f"🔍 文件大小: {os.path.getsize(image_path)} 字节")
                
                # 尝试多种方式加载图片
                pixmap = None
                
                # 方式1: 直接加载
                try:
                    pixmap = QPixmap(image_path)
                    print(f"🔍 直接加载结果: isNull={pixmap.isNull()}, size={pixmap.size()}")
                except Exception as e:
                    print(f"❌ 直接加载失败: {e}")
                
                # 方式2: 使用QPixmap.fromImage
                if pixmap is None or pixmap.isNull():
                    try:
                        from PyQt5.QtGui import QImage
                        image = QImage(image_path)
                        if not image.isNull():
                            pixmap = QPixmap.fromImage(image)
                            print(f"🔍 QImage加载结果: isNull={pixmap.isNull()}, size={pixmap.size()}")
                    except Exception as e:
                        print(f"❌ QImage加载失败: {e}")
                
                # 方式3: 读取字节数据
                if pixmap is None or pixmap.isNull():
                    try:
                        with open(image_path, 'rb') as f:
                            data = f.read()
                        pixmap = QPixmap()
                        pixmap.loadFromData(data)
                        print(f"🔍 字节数据加载结果: isNull={pixmap.isNull()}, size={pixmap.size()}")
                    except Exception as e:
                        print(f"❌ 字节数据加载失败: {e}")
                
                # 如果成功加载图片
                if pixmap and not pixmap.isNull():
                    # 缩放图片到合适大小
                    scaled_pixmap = pixmap.scaled(300, 300, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                    image_label.setPixmap(scaled_pixmap)
                    print("✅ 联系方式图片加载成功")
                    
                    # 延迟更新，确保显示
                    def update_display():
                        image_label.update()
                        image_label.repaint()
                    
                    QTimer.singleShot(100, update_display)
                else:
                    print("❌ 所有加载方式都失败")
                    image_label.setText("📱 联系方式图片\\n(图片加载失败)")
                    image_label.setStyleSheet(image_label.styleSheet() + "font-size: 14pt; color: #e74c3c;")
            else:
                print(f"❌ 图片文件不存在: {image_path}")
                image_label.setText("📱 联系方式图片\\n(12.jpg 文件未找到)")
                image_label.setStyleSheet(image_label.styleSheet() + "font-size: 14pt; color: #e74c3c;")
                
        except Exception as e:
            print(f"❌ 图片加载异常: {str(e)}")
            image_label.setText(f"📱 联系方式图片\\n(加载异常: {str(e)})")
            image_label.setStyleSheet(image_label.styleSheet() + "font-size: 12pt; color: #e74c3c;")'''
    
    # 替换图片加载逻辑
    content = content.replace(old_image_loading, new_image_loading)
    
    # 保存文件
    with open(help_window_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ 帮助窗口图片加载逻辑已修复")
    return True

def create_final_spec():
    """创建最终修复版spec文件"""
    print("📝 创建最终修复版spec文件...")
    
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

import os
from pathlib import Path

# 项目根目录
project_root = os.path.dirname(os.path.abspath(SPEC))

# 数据文件 - 确保图片文件在多个位置
datas = []

# 配置文件
config_files = ['config-template.json', 'config-gui.json']
for config_file in config_files:
    config_path = os.path.join(project_root, config_file)
    if os.path.exists(config_path):
        datas.append((config_path, '.'))

# 图片文件 - 放在多个位置确保能找到
image_files = ['2.ico', '12.jpg', '33.png']
for image_file in image_files:
    image_path = os.path.join(project_root, image_file)
    if os.path.exists(image_path):
        # 根目录
        datas.append((image_path, '.'))
        # images目录
        datas.append((image_path, 'images'))
        # assets目录
        datas.append((image_path, 'assets'))
        print(f"添加图片文件到多个位置: {image_file}")

# ntwork目录
ntwork_path = os.path.join(project_root, 'ntwork')
if os.path.exists(ntwork_path):
    for root, dirs, files in os.walk(ntwork_path):
        for file in files:
            if not file.endswith('.pyc') and '__pycache__' not in root:
                src_path = os.path.join(root, file)
                rel_path = os.path.relpath(src_path, project_root)
                dest_path = os.path.dirname(rel_path)
                datas.append((src_path, dest_path))

# 隐藏导入
hiddenimports = [
    'PyQt5.QtCore', 'PyQt5.QtGui', 'PyQt5.QtWidgets', 'PyQt5.sip', 'PyQt5.QtPrintSupport',
    'PIL', 'PIL.Image', 'PIL.ImageTk', 'PIL.ImageDraw', 'PIL.ImageFont', 'PIL.ImageFilter',
    'PIL.ImageEnhance', 'PIL.ImageOps', 'PIL.ImageChops', 'PIL.ImageStat', 'PIL.ImageColor',
    'PIL.ImageFile', 'PIL.ImageSequence', 'PIL.ImageShow', 'PIL.ImageWin', 'PIL.ImageGrab',
    'PIL.ImagePath', 'PIL.ImageQt', 'PIL.ImageCms', 'PIL.ImageMath', 'PIL.ImageMode',
    'PIL.ImagePalette', 'PIL.ImageTransform', 'PIL._imaging', 'PIL._imagingft',
    'PIL._imagingmath', 'PIL._imagingmorph', 'PIL._imagingcms', 'PIL._webp', 'PIL._tkinter_finder',
    'ntwork', 'ntwork.core', 'ntwork.core.wework', 'ntwork.core.mgr', 'ntwork.utils',
    'ntwork.utils.logger', 'ntwork.utils.singleton', 'ntwork.utils.xdg', 'ntwork.const',
    'ntwork.const.notify_type', 'ntwork.const.send_type', 'ntwork.exception', 'ntwork.wc',
    'pilk', 'pyee', 'pyee.base', 'pyee.asyncio', 'websockets', 'websockets.client',
    'websockets.server', 'aiohttp', 'aiohttp.client', 'aiohttp.web',
    'gui.main_window', 'gui.controllers', 'gui.config_loader', 'gui.managers',
    'gui.system_tray', 'gui.help_window', 'gui.donate_window', 'config',
    'common.log', 'common.utils', 'common.singleton', 'common.resource_manager',
    'bridge.bridge', 'bridge.context', 'bridge.reply', 'bot.bot_factory',
    'channel.channel_factory', 'plugins.plugin_manager',
    'requests', 'json', 'threading', 'queue', 'sqlite3', 'configparser', 'logging',
    'ctypes', 'ctypes.wintypes', 'win32api', 'win32con', 'win32gui', 'pywintypes',
    'asyncio', 'typing_extensions', 'dataclasses',
]

excludes = ['matplotlib', 'scipy', 'tensorflow', 'torch', 'tkinter', 'pygame', 'voice']

block_cipher = None

a = Analysis(
    ['gui_app.py'],
    pathex=[project_root],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=excludes,
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

icon_path = os.path.join(project_root, '2.ico')

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='企业微信自动回复系统_最终版',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=False,
    console=False,  # 无控制台版本
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=icon_path if os.path.exists(icon_path) else None,
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=False,
    upx_exclude=[],
    name='企业微信自动回复系统_最终版',
)
'''
    
    with open('final_pack.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✅ 最终修复版spec文件创建完成")

def copy_images_to_assets():
    """将图片复制到assets目录"""
    print("📁 复制图片到assets目录...")
    
    # 创建assets目录
    assets_dir = Path("assets")
    if not assets_dir.exists():
        assets_dir.mkdir()
    
    image_files = ["2.ico", "12.jpg", "33.png"]
    
    for image_file in image_files:
        if Path(image_file).exists():
            # 复制到assets目录
            shutil.copy2(image_file, assets_dir / image_file)
            print(f"✅ 复制 {image_file} 到 assets/ 目录")
        else:
            print(f"❌ 源文件不存在: {image_file}")

def main():
    """主函数"""
    print("🔧 最终图片显示修复方案")
    print("=" * 50)
    
    # 修复帮助窗口图片加载逻辑
    fix_help_window_image_loading()
    
    # 复制图片到assets目录
    copy_images_to_assets()
    
    # 创建最终spec文件
    create_final_spec()
    
    print("\n" + "=" * 50)
    print("🎉 最终修复完成！")
    print("=" * 50)
    print("💡 执行步骤:")
    print("1. 运行: pack_env\\Scripts\\python.exe -m PyInstaller final_pack.spec")
    print("2. 运行: python fix_qt_plugins.py")
    print("3. 测试: 运行生成的exe文件")
    print("\n✅ 这次应该能彻底解决图片显示问题！")
    print("📋 修复要点:")
    print("   - 多种图片加载方式（直接加载、QImage、字节数据）")
    print("   - 图片文件放在多个位置（根目录、images、assets）")
    print("   - 详细的调试输出")
    print("   - 延迟更新确保显示")

if __name__ == "__main__":
    main()
