([('企业微信自动回复系统_终极解决版.exe',
   'E:\\Desktop\\企业微信\\build\\ultimate_solution\\企业微信自动回复系统_终极解决版.exe',
   'EXECUTABLE'),
  ('python310.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\python310.dll',
   'BINARY'),
  ('VCRUNTIME140.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\VCRUNTIME140.dll',
   'BINARY'),
  ('pywin32_system32\\pythoncom310.dll',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pywin32_system32\\pythoncom310.dll',
   'BINARY'),
  ('pywin32_system32\\pywintypes310.dll',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pywin32_system32\\pywintypes310.dll',
   'BINARY'),
  ('select',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\select.pyd',
   'EXTENSION'),
  ('_lzma',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\_lzma.pyd',
   'EXTENSION'),
  ('_bz2',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\_bz2.pyd',
   'EXTENSION'),
  ('_socket',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('_decimal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_hashlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('_uuid',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\_uuid.pyd',
   'EXTENSION'),
  ('_ssl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\_ssl.pyd',
   'EXTENSION'),
  ('win32com.shell.shell',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\win32comext\\shell\\shell.pyd',
   'EXTENSION'),
  ('win32trace',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\win32\\win32trace.pyd',
   'EXTENSION'),
  ('win32ui',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\Pythonwin\\win32ui.pyd',
   'EXTENSION'),
  ('win32event', 'E:\\Desktop\\企业微信\\win32event.pyd', 'EXTENSION'),
  ('pyexpat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\pyexpat.pyd',
   'EXTENSION'),
  ('_multiprocessing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('_overlapped',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_asyncio',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\_asyncio.pyd',
   'EXTENSION'),
  ('_win32sysloader',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\win32\\_win32sysloader.pyd',
   'EXTENSION'),
  ('win32gui', 'E:\\Desktop\\企业微信\\win32gui.pyd', 'EXTENSION'),
  ('win32api', 'E:\\Desktop\\企业微信\\win32api.pyd', 'EXTENSION'),
  ('_ctypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('_sqlite3',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\_sqlite3.pyd',
   'EXTENSION'),
  ('_queue',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\_queue.pyd',
   'EXTENSION'),
  ('unicodedata',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('charset_normalizer.md__mypyc',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\charset_normalizer\\md__mypyc.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer.md',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\charset_normalizer\\md.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('_elementtree',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\_elementtree.pyd',
   'EXTENSION'),
  ('win32evtlog',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\win32\\win32evtlog.pyd',
   'EXTENSION'),
  ('aiohttp._websocket.mask',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\aiohttp\\_websocket\\mask.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('aiohttp._http_writer',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\aiohttp\\_http_writer.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('aiohttp._http_parser',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\aiohttp\\_http_parser.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('yarl._quoting_c',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\yarl\\_quoting_c.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('propcache._helpers_c',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\propcache\\_helpers_c.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('frozenlist._frozenlist',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\frozenlist\\_frozenlist.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('aiohttp._websocket.reader_c',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\aiohttp\\_websocket\\reader_c.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('multidict._multidict',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\multidict\\_multidict.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('websockets.speedups',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\websockets\\speedups.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pilk._pilk',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\pilk\\_pilk.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('ntwork.wc.wcprobe',
   'E:\\Desktop\\企业微信\\ntwork\\wc\\wcprobe.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('PIL._webp',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\PIL\\_webp.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('PIL._imagingcms',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\PIL\\_imagingcms.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('PIL._imagingmorph',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\PIL\\_imagingmorph.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('PIL._imagingmath',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\PIL\\_imagingmath.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('PIL._imagingft',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\PIL\\_imagingft.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('PIL._imaging',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\PIL\\_imaging.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy._core._multiarray_tests',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\_core\\_multiarray_tests.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy._core._multiarray_umath',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\_core\\_multiarray_umath.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('win32pdh',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\win32\\win32pdh.pyd',
   'EXTENSION'),
  ('numpy.linalg._umath_linalg',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\linalg\\_umath_linalg.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy.random.mtrand',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\random\\mtrand.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy.random._sfc64',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\random\\_sfc64.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy.random._philox',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\random\\_philox.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy.random._pcg64',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\random\\_pcg64.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy.random._mt19937',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\random\\_mt19937.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy.random.bit_generator',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\random\\bit_generator.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy.random._generator',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\random\\_generator.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy.random._bounded_integers',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\random\\_bounded_integers.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy.random._common',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\random\\_common.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy.fft._pocketfft_umath',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy\\fft\\_pocketfft_umath.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('PIL._imagingtk',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\PIL\\_imagingtk.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('PIL._avif',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\PIL\\_avif.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('PyQt5.QtPrintSupport',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\PyQt5\\QtPrintSupport.pyd',
   'EXTENSION'),
  ('PyQt5.sip',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\PyQt5\\sip.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('PyQt5.QtCore',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\PyQt5\\QtCore.pyd',
   'EXTENSION'),
  ('PyQt5.QtGui',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\PyQt5\\QtGui.pyd',
   'EXTENSION'),
  ('PyQt5.QtWidgets',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\PyQt5\\QtWidgets.pyd',
   'EXTENSION'),
  ('VCRUNTIME140_1.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('libcrypto-1_1.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\libcrypto-1_1.dll',
   'BINARY'),
  ('libssl-1_1.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\libssl-1_1.dll',
   'BINARY'),
  ('pywintypes310.dll', 'E:\\Desktop\\企业微信\\pywintypes310.dll', 'BINARY'),
  ('pythoncom310.dll', 'E:\\Desktop\\企业微信\\pythoncom310.dll', 'BINARY'),
  ('mfc140u.dll',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\Pythonwin\\mfc140u.dll',
   'BINARY'),
  ('libffi-7.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\libffi-7.dll',
   'BINARY'),
  ('sqlite3.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\sqlite3.dll',
   'BINARY'),
  ('numpy.libs\\msvcp140-263139962577ecda4cd9469ca360a746.dll',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy.libs\\msvcp140-263139962577ecda4cd9469ca360a746.dll',
   'BINARY'),
  ('numpy.libs\\libscipy_openblas64_-13e2df515630b4a41f92893938845698.dll',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy.libs\\libscipy_openblas64_-13e2df515630b4a41f92893938845698.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Gui.dll',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Gui.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5PrintSupport.dll',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5PrintSupport.dll',
   'BINARY'),
  ('python3.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\python3.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Widgets.dll',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Widgets.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Core.dll',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Core.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\MSVCP140.dll',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\PyQt5\\Qt5\\bin\\MSVCP140.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\MSVCP140_1.dll',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\PyQt5\\Qt5\\bin\\MSVCP140_1.dll',
   'BINARY'),
  ('base_library.zip',
   'E:\\Desktop\\企业微信\\build\\ultimate_solution\\base_library.zip',
   'DATA'),
  ('certifi\\py.typed',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\certifi\\py.typed',
   'DATA'),
  ('certifi\\cacert.pem',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('numpy-2.2.6.dist-info\\WHEEL',
   'e:\\desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy-2.2.6.dist-info\\WHEEL',
   'DATA'),
  ('numpy-2.2.6.dist-info\\METADATA',
   'e:\\desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy-2.2.6.dist-info\\METADATA',
   'DATA'),
  ('numpy-2.2.6.dist-info\\RECORD',
   'e:\\desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy-2.2.6.dist-info\\RECORD',
   'DATA'),
  ('attrs-25.3.0.dist-info\\INSTALLER',
   'e:\\desktop\\企业微信\\pack_env\\lib\\site-packages\\attrs-25.3.0.dist-info\\INSTALLER',
   'DATA'),
  ('numpy-2.2.6.dist-info\\INSTALLER',
   'e:\\desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy-2.2.6.dist-info\\INSTALLER',
   'DATA'),
  ('websockets-15.0.1.dist-info\\LICENSE',
   'e:\\desktop\\企业微信\\pack_env\\lib\\site-packages\\websockets-15.0.1.dist-info\\LICENSE',
   'DATA'),
  ('numpy-2.2.6.dist-info\\entry_points.txt',
   'e:\\desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy-2.2.6.dist-info\\entry_points.txt',
   'DATA'),
  ('numpy-2.2.6.dist-info\\REQUESTED',
   'e:\\desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy-2.2.6.dist-info\\REQUESTED',
   'DATA'),
  ('attrs-25.3.0.dist-info\\licenses\\LICENSE',
   'e:\\desktop\\企业微信\\pack_env\\lib\\site-packages\\attrs-25.3.0.dist-info\\licenses\\LICENSE',
   'DATA'),
  ('websockets-15.0.1.dist-info\\INSTALLER',
   'e:\\desktop\\企业微信\\pack_env\\lib\\site-packages\\websockets-15.0.1.dist-info\\INSTALLER',
   'DATA'),
  ('websockets-15.0.1.dist-info\\REQUESTED',
   'e:\\desktop\\企业微信\\pack_env\\lib\\site-packages\\websockets-15.0.1.dist-info\\REQUESTED',
   'DATA'),
  ('websockets-15.0.1.dist-info\\METADATA',
   'e:\\desktop\\企业微信\\pack_env\\lib\\site-packages\\websockets-15.0.1.dist-info\\METADATA',
   'DATA'),
  ('numpy-2.2.6.dist-info\\DELVEWHEEL',
   'e:\\desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy-2.2.6.dist-info\\DELVEWHEEL',
   'DATA'),
  ('websockets-15.0.1.dist-info\\RECORD',
   'e:\\desktop\\企业微信\\pack_env\\lib\\site-packages\\websockets-15.0.1.dist-info\\RECORD',
   'DATA'),
  ('websockets-15.0.1.dist-info\\entry_points.txt',
   'e:\\desktop\\企业微信\\pack_env\\lib\\site-packages\\websockets-15.0.1.dist-info\\entry_points.txt',
   'DATA'),
  ('websockets-15.0.1.dist-info\\WHEEL',
   'e:\\desktop\\企业微信\\pack_env\\lib\\site-packages\\websockets-15.0.1.dist-info\\WHEEL',
   'DATA'),
  ('attrs-25.3.0.dist-info\\WHEEL',
   'e:\\desktop\\企业微信\\pack_env\\lib\\site-packages\\attrs-25.3.0.dist-info\\WHEEL',
   'DATA'),
  ('websockets-15.0.1.dist-info\\top_level.txt',
   'e:\\desktop\\企业微信\\pack_env\\lib\\site-packages\\websockets-15.0.1.dist-info\\top_level.txt',
   'DATA'),
  ('attrs-25.3.0.dist-info\\RECORD',
   'e:\\desktop\\企业微信\\pack_env\\lib\\site-packages\\attrs-25.3.0.dist-info\\RECORD',
   'DATA'),
  ('numpy-2.2.6.dist-info\\LICENSE.txt',
   'e:\\desktop\\企业微信\\pack_env\\lib\\site-packages\\numpy-2.2.6.dist-info\\LICENSE.txt',
   'DATA'),
  ('attrs-25.3.0.dist-info\\METADATA',
   'e:\\desktop\\企业微信\\pack_env\\lib\\site-packages\\attrs-25.3.0.dist-info\\METADATA',
   'DATA'),
  ('ntwork\\wc\\__init__.py',
   'E:\\Desktop\\企业微信\\ntwork\\wc\\__init__.py',
   'DATA'),
  ('ntwork\\wc\\helper_4.0.8.6027.dat',
   'E:\\Desktop\\企业微信\\ntwork\\wc\\helper_4.0.8.6027.dat',
   'DATA'),
  ('ntwork\\exception\\__init__.py',
   'E:\\Desktop\\企业微信\\ntwork\\exception\\__init__.py',
   'DATA'),
  ('ntwork\\wc\\wcprobe.cp310-win_amd64.pyd',
   'E:\\Desktop\\企业微信\\ntwork\\wc\\wcprobe.cp310-win_amd64.pyd',
   'DATA'),
  ('ntwork\\utils\\xdg.py', 'E:\\Desktop\\企业微信\\ntwork\\utils\\xdg.py', 'DATA'),
  ('ntwork\\core\\mgr.py', 'E:\\Desktop\\企业微信\\ntwork\\core\\mgr.py', 'DATA'),
  ('config-gui.json', 'E:\\Desktop\\企业微信\\config-gui.json', 'DATA'),
  ('ntwork\\core\\wework.py',
   'E:\\Desktop\\企业微信\\ntwork\\core\\wework.py',
   'DATA'),
  ('ntwork\\utils\\__init__.py',
   'E:\\Desktop\\企业微信\\ntwork\\utils\\__init__.py',
   'DATA'),
  ('config-template.json', 'E:\\Desktop\\企业微信\\config-template.json', 'DATA'),
  ('ntwork\\const\\send_type.py',
   'E:\\Desktop\\企业微信\\ntwork\\const\\send_type.py',
   'DATA'),
  ('ntwork\\utils\\logger.py',
   'E:\\Desktop\\企业微信\\ntwork\\utils\\logger.py',
   'DATA'),
  ('ntwork\\core\\__init__.py',
   'E:\\Desktop\\企业微信\\ntwork\\core\\__init__.py',
   'DATA'),
  ('ntwork\\utils\\singleton.py',
   'E:\\Desktop\\企业微信\\ntwork\\utils\\singleton.py',
   'DATA'),
  ('ntwork\\const\\notify_type.py',
   'E:\\Desktop\\企业微信\\ntwork\\const\\notify_type.py',
   'DATA'),
  ('ntwork\\conf\\__init__.py',
   'E:\\Desktop\\企业微信\\ntwork\\conf\\__init__.py',
   'DATA'),
  ('ntwork\\const\\__init__.py',
   'E:\\Desktop\\企业微信\\ntwork\\const\\__init__.py',
   'DATA'),
  ('ntwork\\__init__.py', 'E:\\Desktop\\企业微信\\ntwork\\__init__.py', 'DATA')],)
