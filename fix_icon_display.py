#!/usr/bin/env python3
# encoding:utf-8
"""
彻底修复图标显示问题
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

def check_image_files():
    """检查图片文件是否存在"""
    print("🔍 检查图片文件...")
    
    image_files = {
        "2.ico": "程序图标",
        "12.jpg": "联系方式图片", 
        "33.png": "捐赠二维码"
    }
    
    all_exist = True
    for filename, description in image_files.items():
        if Path(filename).exists():
            size = Path(filename).stat().st_size
            print(f"✅ {filename} ({description}): {size} 字节")
        else:
            print(f"❌ {filename} ({description}): 不存在")
            all_exist = False
    
    return all_exist

def create_test_images():
    """创建测试图片（如果原图片有问题）"""
    print("🎨 创建测试图片...")
    
    try:
        from PIL import Image, ImageDraw, ImageFont
        
        # 创建测试的联系方式图片
        if not Path("12.jpg").exists() or Path("12.jpg").stat().st_size < 1000:
            print("创建测试联系方式图片...")
            img = Image.new('RGB', (300, 300), color='white')
            draw = ImageDraw.Draw(img)
            
            # 绘制边框
            draw.rectangle([10, 10, 290, 290], outline='blue', width=3)
            
            # 绘制文字
            try:
                # 尝试使用系统字体
                font = ImageFont.truetype("arial.ttf", 20)
            except:
                font = ImageFont.load_default()
            
            draw.text((50, 100), "联系方式", fill='black', font=font)
            draw.text((50, 140), "Contact Info", fill='blue', font=font)
            draw.text((50, 180), "微信: WeChat", fill='green', font=font)
            draw.text((50, 220), "QQ: 123456789", fill='red', font=font)
            
            img.save("12.jpg", "JPEG", quality=95)
            print("✅ 测试联系方式图片创建成功")
        
        # 创建测试的捐赠二维码
        if not Path("33.png").exists() or Path("33.png").stat().st_size < 1000:
            print("创建测试捐赠二维码...")
            img = Image.new('RGB', (280, 280), color='white')
            draw = ImageDraw.Draw(img)
            
            # 绘制边框
            draw.rectangle([10, 10, 270, 270], outline='green', width=3)
            
            # 绘制简单的二维码样式
            for i in range(20, 260, 20):
                for j in range(20, 260, 20):
                    if (i + j) % 40 == 0:
                        draw.rectangle([i, j, i+15, j+15], fill='black')
            
            # 绘制文字
            try:
                font = ImageFont.truetype("arial.ttf", 16)
            except:
                font = ImageFont.load_default()
            
            draw.text((80, 250), "请我喝茶二维码", fill='green', font=font)
            
            img.save("33.png", "PNG")
            print("✅ 测试捐赠二维码创建成功")
            
        return True
        
    except ImportError:
        print("⚠️ PIL库不可用，无法创建测试图片")
        return False
    except Exception as e:
        print(f"❌ 创建测试图片失败: {e}")
        return False

def fix_icon_in_exe():
    """修复exe文件中的图标"""
    print("🔧 修复exe文件图标...")
    
    # 检查是否有工具可以修改exe图标
    try:
        # 尝试使用ResourceHacker（如果可用）
        rh_path = Path("ResourceHacker.exe")
        if rh_path.exists():
            print("找到ResourceHacker，尝试修复图标...")
            # 这里可以添加ResourceHacker的命令
        else:
            print("ResourceHacker不可用，使用PyInstaller重新打包")
            
    except Exception as e:
        print(f"图标修复工具不可用: {e}")

def create_icon_fix_spec():
    """创建专门修复图标的spec文件"""
    print("📝 创建图标修复专用spec文件...")
    
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

import os
from pathlib import Path

# 项目根目录
project_root = os.path.dirname(os.path.abspath(SPEC))

# 确保图片文件存在
required_images = ['2.ico', '12.jpg', '33.png']
missing_images = []
for img in required_images:
    img_path = os.path.join(project_root, img)
    if not os.path.exists(img_path):
        missing_images.append(img)

if missing_images:
    print(f"警告: 缺少图片文件: {missing_images}")

# 数据文件 - 强制包含所有图片
datas = []

# 添加配置文件
config_files = ['config-template.json', 'config-gui.json']
for config_file in config_files:
    config_path = os.path.join(project_root, config_file)
    if os.path.exists(config_path):
        datas.append((config_path, '.'))

# 添加图片文件 - 使用绝对路径确保找到
image_files = ['2.ico', '12.jpg', '33.png']
for image_file in image_files:
    image_path = os.path.join(project_root, image_file)
    if os.path.exists(image_path):
        datas.append((image_path, '.'))
        print(f"添加图片文件: {image_path}")
    else:
        print(f"警告: 图片文件不存在: {image_path}")

# 手动添加ntwork目录
ntwork_path = os.path.join(project_root, 'ntwork')
if os.path.exists(ntwork_path):
    for root, dirs, files in os.walk(ntwork_path):
        for file in files:
            if not file.endswith('.pyc') and '__pycache__' not in root:
                src_path = os.path.join(root, file)
                rel_path = os.path.relpath(src_path, project_root)
                dest_path = os.path.dirname(rel_path)
                datas.append((src_path, dest_path))

# 包含所有必要的隐藏导入
hiddenimports = [
    # PyQt5相关
    'PyQt5.QtCore',
    'PyQt5.QtGui', 
    'PyQt5.QtWidgets',
    'PyQt5.sip',
    'PyQt5.QtPrintSupport',
    
    # PIL/Pillow相关
    'PIL',
    'PIL.Image',
    'PIL.ImageTk',
    'PIL.ImageDraw',
    'PIL.ImageFont',
    'PIL.ImageFilter',
    'PIL.ImageEnhance',
    'PIL.ImageOps',
    'PIL.ImageChops',
    'PIL.ImageStat',
    'PIL.ImageColor',
    'PIL.ImageFile',
    'PIL.ImageSequence',
    'PIL.ImageShow',
    'PIL.ImageWin',
    'PIL.ImageGrab',
    'PIL.ImagePath',
    'PIL.ImageQt',
    'PIL.ImageCms',
    'PIL.ImageMath',
    'PIL.ImageMode',
    'PIL.ImagePalette',
    'PIL.ImageTransform',
    'PIL._imaging',
    'PIL._imagingft',
    'PIL._imagingmath',
    'PIL._imagingmorph',
    'PIL._imagingcms',
    'PIL._webp',
    'PIL._tkinter_finder',
    
    # ntwork相关
    'ntwork',
    'ntwork.core',
    'ntwork.core.wework',
    'ntwork.core.mgr',
    'ntwork.utils',
    'ntwork.utils.logger',
    'ntwork.utils.singleton',
    'ntwork.utils.xdg',
    'ntwork.const',
    'ntwork.const.notify_type',
    'ntwork.const.send_type',
    'ntwork.exception',
    'ntwork.wc',
    
    # ntwork依赖
    'pilk',
    'pyee',
    'pyee.base',
    'pyee.asyncio',
    'websockets',
    'websockets.client',
    'websockets.server',
    'aiohttp',
    'aiohttp.client',
    'aiohttp.web',
    
    # 应用程序模块
    'gui.main_window',
    'gui.controllers',
    'gui.config_loader',
    'gui.managers',
    'gui.system_tray',
    'gui.help_window',
    'gui.donate_window',
    'config',
    'common.log',
    'common.utils',
    'common.singleton',
    'bridge.bridge',
    'bridge.context',
    'bridge.reply',
    'bot.bot_factory',
    'channel.channel_factory',
    'plugins.plugin_manager',
    
    # 其他依赖
    'requests',
    'json',
    'threading',
    'queue',
    'sqlite3',
    'configparser',
    'logging',
    'ctypes',
    'ctypes.wintypes',
    'win32api',
    'win32con',
    'win32gui',
    'pywintypes',
    'asyncio',
    'typing_extensions',
    'dataclasses',
]

# 排除模块
excludes = [
    'matplotlib',
    'scipy',
    'tensorflow',
    'torch',
    'tkinter',
    'pygame',
    'voice',
]

block_cipher = None

a = Analysis(
    ['gui_app.py'],
    pathex=[project_root],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=excludes,
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

# 确保图标文件存在
icon_path = os.path.join(project_root, '2.ico')
if not os.path.exists(icon_path):
    print(f"警告: 图标文件不存在: {icon_path}")
    icon_path = None

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='企业微信自动回复系统_图标显示修复版',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=False,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=icon_path,  # 使用检查后的图标路径
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=False,
    upx_exclude=[],
    name='企业微信自动回复系统_图标显示修复版',
)
'''
    
    with open('icon_display_fix.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✅ 图标显示修复spec文件创建完成")

def create_runtime_icon_fix():
    """创建运行时图标修复脚本"""
    print("🔧 创建运行时图标修复...")
    
    fix_script = '''#!/usr/bin/env python3
# encoding:utf-8
"""
运行时图标修复脚本
"""

import os
import sys
from pathlib import Path
from PyQt5.QtGui import QIcon, QPixmap
from PyQt5.QtCore import QSize

def fix_window_icon(window):
    """修复窗口图标"""
    try:
        # 获取当前目录
        current_dir = Path(__file__).parent if hasattr(__file__, '__file__') else Path.cwd()
        
        # 尝试多个可能的图标路径
        icon_paths = [
            current_dir / "2.ico",
            current_dir / "icon.ico",
            Path(sys.executable).parent / "2.ico",
            Path(os.getcwd()) / "2.ico"
        ]
        
        for icon_path in icon_paths:
            if icon_path.exists():
                try:
                    icon = QIcon(str(icon_path))
                    if not icon.isNull():
                        window.setWindowIcon(icon)
                        print(f"✅ 窗口图标设置成功: {icon_path}")
                        return True
                except Exception as e:
                    print(f"⚠️ 图标加载失败 {icon_path}: {e}")
                    continue
        
        # 如果所有路径都失败，创建默认图标
        print("🎨 创建默认图标...")
        pixmap = QPixmap(32, 32)
        pixmap.fill()
        
        from PyQt5.QtGui import QPainter, QBrush, QColor
        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # 绘制一个简单的图标
        painter.setBrush(QBrush(QColor(0, 120, 215)))  # 蓝色
        painter.drawEllipse(4, 4, 24, 24)
        
        # 绘制字母"W"
        painter.setPen(QColor(255, 255, 255))
        painter.drawText(12, 20, "W")
        
        painter.end()
        
        icon = QIcon(pixmap)
        window.setWindowIcon(icon)
        print("✅ 默认图标设置成功")
        return True
        
    except Exception as e:
        print(f"❌ 图标修复失败: {e}")
        return False

def fix_image_loading(image_label, image_filename, description):
    """修复图片加载"""
    try:
        # 获取当前目录
        current_dir = Path(__file__).parent if hasattr(__file__, '__file__') else Path.cwd()
        
        # 尝试多个可能的图片路径
        image_paths = [
            current_dir / image_filename,
            Path(sys.executable).parent / image_filename,
            Path(os.getcwd()) / image_filename
        ]
        
        for image_path in image_paths:
            if image_path.exists():
                try:
                    pixmap = QPixmap(str(image_path))
                    if not pixmap.isNull():
                        # 缩放图片到合适大小
                        if image_filename.endswith('.jpg'):
                            scaled_pixmap = pixmap.scaled(300, 300, 1, 1)  # Qt.KeepAspectRatio, Qt.SmoothTransformation
                        else:
                            scaled_pixmap = pixmap.scaled(280, 280, 1, 1)
                        
                        image_label.setPixmap(scaled_pixmap)
                        print(f"✅ {description}加载成功: {image_path}")
                        return True
                except Exception as e:
                    print(f"⚠️ 图片加载失败 {image_path}: {e}")
                    continue
        
        # 如果所有路径都失败，显示提示文字
        image_label.setText(f"📱 {description}\\n({image_filename} 文件未找到)")
        print(f"❌ {description}未找到: {image_filename}")
        return False
        
    except Exception as e:
        print(f"❌ 图片修复失败: {e}")
        image_label.setText(f"📱 {description}\\n(加载失败: {str(e)})")
        return False

# 导出修复函数
__all__ = ['fix_window_icon', 'fix_image_loading']
'''
    
    with open('runtime_icon_fix.py', 'w', encoding='utf-8') as f:
        f.write(fix_script)
    
    print("✅ 运行时图标修复脚本创建完成")

def main():
    """主函数"""
    print("🖼️ 图标显示彻底修复工具")
    print("=" * 50)
    
    # 检查图片文件
    if not check_image_files():
        print("⚠️ 部分图片文件缺失，尝试创建测试图片...")
        create_test_images()
    
    # 创建图标修复spec
    create_icon_fix_spec()
    
    # 创建运行时修复脚本
    create_runtime_icon_fix()
    
    print("\n" + "=" * 50)
    print("🎉 图标显示修复工具完成！")
    print("=" * 50)
    print("💡 修复步骤:")
    print("1. 运行: pack_env\\Scripts\\python.exe -m PyInstaller icon_display_fix.spec")
    print("2. 运行: python fix_qt_plugins.py")
    print("3. 测试: 运行生成的exe文件")
    print("4. 如果还有问题，检查runtime_icon_fix.py中的修复逻辑")
    print("\n✅ 这次应该能完全解决图标显示问题！")

if __name__ == "__main__":
    main()
