#!/usr/bin/env python3
# encoding:utf-8
"""
创建将图标和图片作为资源嵌入到spec文件中的解决方案
"""

import os
import sys
import base64
from pathlib import Path

def read_and_encode_files():
    """读取并编码图片文件"""
    print("📦 读取并编码图片文件...")
    
    files_to_encode = {
        "2.ico": "程序图标",
        "12.jpg": "联系方式图片",
        "33.png": "捐赠二维码"
    }
    
    encoded_files = {}
    
    for filename, description in files_to_encode.items():
        file_path = Path(filename)
        if file_path.exists():
            with open(file_path, 'rb') as f:
                data = f.read()
            encoded = base64.b64encode(data).decode('utf-8')
            encoded_files[filename] = encoded
            print(f"✅ {description} ({filename}): {len(data)} 字节 -> {len(encoded)} 字符")
        else:
            print(f"❌ {description} ({filename}): 文件不存在")
            encoded_files[filename] = ""
    
    return encoded_files

def create_resource_embedded_spec():
    """创建将资源嵌入到spec文件中的版本"""
    print("📝 创建资源嵌入版spec文件...")
    
    # 读取并编码文件
    encoded_files = read_and_encode_files()
    
    spec_content = f'''# -*- mode: python ; coding: utf-8 -*-
# 资源嵌入版 - 将图标和图片直接嵌入到spec文件中

import os
import base64
import tempfile
from pathlib import Path

project_root = os.path.dirname(os.path.abspath(SPEC))

# 嵌入的资源数据（base64编码）
EMBEDDED_RESOURCES = {{
    "2.ico": """{encoded_files.get("2.ico", "")}"",
    "12.jpg": """{encoded_files.get("12.jpg", "")}"",
    "33.png": """{encoded_files.get("33.png", "")}"",
}}

def create_resource_files():
    """从嵌入的数据创建临时资源文件"""
    temp_files = []
    
    for filename, encoded_data in EMBEDDED_RESOURCES.items():
        if encoded_data:
            try:
                # 解码数据
                data = base64.b64decode(encoded_data)
                
                # 创建临时文件
                suffix = Path(filename).suffix
                temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=suffix)
                temp_file.write(data)
                temp_file.close()
                
                # 复制到项目根目录
                target_path = os.path.join(project_root, filename)
                import shutil
                shutil.copy2(temp_file.name, target_path)
                
                temp_files.append((temp_file.name, target_path))
                print(f"✅ 创建资源文件: {{filename}} -> {{target_path}}")
                
            except Exception as e:
                print(f"❌ 创建资源文件失败 {{filename}}: {{e}}")
    
    return temp_files

# 在分析前创建资源文件
print("🔧 从嵌入数据创建资源文件...")
temp_files = create_resource_files()

# 数据文件 - 包含从嵌入数据创建的资源文件
datas = []

# 添加资源文件
resource_files = ['2.ico', '12.jpg', '33.png']
for resource_file in resource_files:
    resource_path = os.path.join(project_root, resource_file)
    if os.path.exists(resource_path):
        datas.append((resource_path, '.'))
        print(f"添加资源文件到打包: {{resource_file}}")

# 配置文件
config_files = ['config-template.json', 'config-gui.json']
for config_file in config_files:
    config_path = os.path.join(project_root, config_file)
    if os.path.exists(config_path):
        datas.append((config_path, '.'))

# ntwork目录
ntwork_path = os.path.join(project_root, 'ntwork')
if os.path.exists(ntwork_path):
    for root, dirs, files in os.walk(ntwork_path):
        for file in files:
            if not file.endswith('.pyc') and '__pycache__' not in root:
                src_path = os.path.join(root, file)
                rel_path = os.path.relpath(src_path, project_root)
                dest_path = os.path.dirname(rel_path)
                datas.append((src_path, dest_path))

# 隐藏导入
hiddenimports = [
    # PyQt5
    'PyQt5.QtCore', 'PyQt5.QtGui', 'PyQt5.QtWidgets', 'PyQt5.sip', 'PyQt5.QtPrintSupport',
    
    # PIL/Pillow
    'PIL', 'PIL.Image', 'PIL.ImageTk', 'PIL.ImageDraw', 'PIL.ImageFont', 'PIL.ImageFilter',
    'PIL.ImageEnhance', 'PIL.ImageOps', 'PIL.ImageChops', 'PIL.ImageStat', 'PIL.ImageColor',
    'PIL.ImageFile', 'PIL.ImageSequence', 'PIL.ImageShow', 'PIL.ImageWin', 'PIL.ImageGrab',
    'PIL.ImagePath', 'PIL.ImageQt', 'PIL.ImageCms', 'PIL.ImageMath', 'PIL.ImageMode',
    'PIL.ImagePalette', 'PIL.ImageTransform', 'PIL._imaging', 'PIL._imagingft',
    'PIL._imagingmath', 'PIL._imagingmorph', 'PIL._imagingcms', 'PIL._webp', 'PIL._tkinter_finder',
    
    # ntwork
    'ntwork', 'ntwork.core', 'ntwork.core.wework', 'ntwork.core.mgr', 'ntwork.utils',
    'ntwork.utils.logger', 'ntwork.utils.singleton', 'ntwork.utils.xdg', 'ntwork.const',
    'ntwork.const.notify_type', 'ntwork.const.send_type', 'ntwork.exception', 'ntwork.wc',
    
    # 依赖
    'pilk', 'pyee', 'pyee.base', 'pyee.asyncio', 'websockets', 'websockets.client',
    'websockets.server', 'aiohttp', 'aiohttp.client', 'aiohttp.web',
    
    # 应用模块
    'gui.main_window', 'gui.controllers', 'gui.config_loader', 'gui.managers',
    'gui.system_tray', 'gui.help_window', 'gui.donate_window', 'config',
    'common.log', 'common.utils', 'common.singleton', 'common.simple_resource_manager',
    'bridge.bridge', 'bridge.context', 'bridge.reply', 'bot.bot_factory',
    'channel.channel_factory', 'plugins.plugin_manager',
    
    # 其他
    'requests', 'json', 'threading', 'queue', 'sqlite3', 'configparser', 'logging',
    'ctypes', 'ctypes.wintypes', 'win32api', 'win32con', 'win32gui', 'pywintypes',
    'asyncio', 'typing_extensions', 'dataclasses', 'base64', 'tempfile', 'shutil',
]

excludes = ['matplotlib', 'scipy', 'tensorflow', 'torch', 'tkinter', 'pygame', 'voice']

block_cipher = None

a = Analysis(
    ['gui_app.py'],
    pathex=[project_root],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=excludes,
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

# 图标文件路径
icon_path = os.path.join(project_root, '2.ico')

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='企业微信自动回复系统_资源嵌入版',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=False,
    console=False,  # 无控制台版本
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=icon_path if os.path.exists(icon_path) else None,
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=False,
    upx_exclude=[],
    name='企业微信自动回复系统_资源嵌入版',
)

# 清理临时文件
print("🗑️ 清理临时文件...")
for temp_file, target_file in temp_files:
    try:
        if os.path.exists(temp_file):
            os.unlink(temp_file)
            print(f"清理临时文件: {{temp_file}}")
    except Exception as e:
        print(f"清理临时文件失败: {{e}}")

print("✅ 资源嵌入版spec文件执行完成！")
'''
    
    with open('resource_embedded.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✅ 资源嵌入版spec文件创建完成")

def create_alternative_spec_with_binaries():
    """创建使用binaries方式嵌入资源的spec文件"""
    print("📝 创建使用binaries方式的spec文件...")
    
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-
# 使用binaries方式嵌入资源

import os
from pathlib import Path

project_root = os.path.dirname(os.path.abspath(SPEC))

# 数据文件
datas = []

# 配置文件
config_files = ['config-template.json', 'config-gui.json']
for config_file in config_files:
    config_path = os.path.join(project_root, config_file)
    if os.path.exists(config_path):
        datas.append((config_path, '.'))

# 图片文件 - 使用多种方式确保包含
image_files = ['2.ico', '12.jpg', '33.png']
for image_file in image_files:
    image_path = os.path.join(project_root, image_file)
    if os.path.exists(image_path):
        # 方式1: 作为数据文件
        datas.append((image_path, '.'))
        # 方式2: 作为二进制文件
        datas.append((image_path, 'images'))
        # 方式3: 作为资源文件
        datas.append((image_path, 'resources'))
        print(f"多重添加图片文件: {image_file}")

# ntwork目录
ntwork_path = os.path.join(project_root, 'ntwork')
if os.path.exists(ntwork_path):
    for root, dirs, files in os.walk(ntwork_path):
        for file in files:
            if not file.endswith('.pyc') and '__pycache__' not in root:
                src_path = os.path.join(root, file)
                rel_path = os.path.relpath(src_path, project_root)
                dest_path = os.path.dirname(rel_path)
                datas.append((src_path, dest_path))

# 二进制文件 - 将图片也作为二进制文件包含
binaries = []
for image_file in image_files:
    image_path = os.path.join(project_root, image_file)
    if os.path.exists(image_path):
        binaries.append((image_path, '.'))

# 隐藏导入
hiddenimports = [
    'PyQt5.QtCore', 'PyQt5.QtGui', 'PyQt5.QtWidgets', 'PyQt5.sip', 'PyQt5.QtPrintSupport',
    'PIL', 'PIL.Image', 'PIL.ImageTk', 'PIL.ImageDraw', 'PIL.ImageFont', 'PIL.ImageFilter',
    'PIL.ImageEnhance', 'PIL.ImageOps', 'PIL.ImageChops', 'PIL.ImageStat', 'PIL.ImageColor',
    'PIL.ImageFile', 'PIL.ImageSequence', 'PIL.ImageShow', 'PIL.ImageWin', 'PIL.ImageGrab',
    'PIL.ImagePath', 'PIL.ImageQt', 'PIL.ImageCms', 'PIL.ImageMath', 'PIL.ImageMode',
    'PIL.ImagePalette', 'PIL.ImageTransform', 'PIL._imaging', 'PIL._imagingft',
    'PIL._imagingmath', 'PIL._imagingmorph', 'PIL._imagingcms', 'PIL._webp', 'PIL._tkinter_finder',
    'ntwork', 'ntwork.core', 'ntwork.core.wework', 'ntwork.core.mgr', 'ntwork.utils',
    'ntwork.utils.logger', 'ntwork.utils.singleton', 'ntwork.utils.xdg', 'ntwork.const',
    'ntwork.const.notify_type', 'ntwork.const.send_type', 'ntwork.exception', 'ntwork.wc',
    'pilk', 'pyee', 'pyee.base', 'pyee.asyncio', 'websockets', 'websockets.client',
    'websockets.server', 'aiohttp', 'aiohttp.client', 'aiohttp.web',
    'gui.main_window', 'gui.controllers', 'gui.config_loader', 'gui.managers',
    'gui.system_tray', 'gui.help_window', 'gui.donate_window', 'config',
    'common.log', 'common.utils', 'common.singleton', 'common.simple_resource_manager',
    'bridge.bridge', 'bridge.context', 'bridge.reply', 'bot.bot_factory',
    'channel.channel_factory', 'plugins.plugin_manager',
    'requests', 'json', 'threading', 'queue', 'sqlite3', 'configparser', 'logging',
    'ctypes', 'ctypes.wintypes', 'win32api', 'win32con', 'win32gui', 'pywintypes',
    'asyncio', 'typing_extensions', 'dataclasses',
]

excludes = ['matplotlib', 'scipy', 'tensorflow', 'torch', 'tkinter', 'pygame', 'voice']

block_cipher = None

a = Analysis(
    ['gui_app.py'],
    pathex=[project_root],
    binaries=binaries,  # 包含二进制文件
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=excludes,
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

icon_path = os.path.join(project_root, '2.ico')

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='企业微信自动回复系统_多重嵌入版',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=False,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=icon_path if os.path.exists(icon_path) else None,
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=False,
    upx_exclude=[],
    name='企业微信自动回复系统_多重嵌入版',
)
'''
    
    with open('multi_embed.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✅ 多重嵌入版spec文件创建完成")

def main():
    """主函数"""
    print("🚀 创建资源嵌入到spec文件的解决方案")
    print("=" * 60)
    
    # 创建资源嵌入版spec
    create_resource_embedded_spec()
    
    # 创建多重嵌入版spec
    create_alternative_spec_with_binaries()
    
    print("\n" + "=" * 60)
    print("🎉 资源嵌入方案创建完成！")
    print("=" * 60)
    print("💡 两种方案:")
    print("")
    print("🔧 方案1: 资源嵌入版 (推荐)")
    print("   - 将图片数据直接编码到spec文件中")
    print("   - 打包时自动创建资源文件")
    print("   - 执行: pack_env\\Scripts\\python.exe -m PyInstaller resource_embedded.spec")
    print("")
    print("🔧 方案2: 多重嵌入版")
    print("   - 使用datas和binaries双重包含")
    print("   - 图片文件放在多个位置")
    print("   - 执行: pack_env\\Scripts\\python.exe -m PyInstaller multi_embed.spec")
    print("")
    print("✅ 两种方案都会:")
    print("   - 确保2.ico程序图标正确嵌入")
    print("   - 确保12.jpg联系方式图片被包含")
    print("   - 确保33.png捐赠二维码被包含")
    print("   - 自动处理Qt插件")

if __name__ == "__main__":
    main()
