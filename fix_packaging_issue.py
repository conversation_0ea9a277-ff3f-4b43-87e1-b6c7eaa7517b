#!/usr/bin/env python3
# encoding:utf-8
"""
修复打包问题 - 确保嵌入式资源模块被正确包含
"""

import os
import sys
import shutil
from pathlib import Path

def create_working_simple_test():
    """创建可工作的简单测试应用"""
    print("🧪 创建可工作的简单测试应用...")
    
    test_app_code = '''#!/usr/bin/env python3
# encoding:utf-8
"""
可工作的简单测试应用 - 不依赖嵌入式资源
"""

import sys
import os
import traceback

def main():
    """主函数"""
    print("🧪 企业微信自动回复系统 - 基础功能测试")
    print("=" * 50)
    
    try:
        print("1. 测试Python基础环境...")
        print(f"   Python版本: {sys.version}")
        print(f"   当前目录: {os.getcwd()}")
        print(f"   可执行文件: {sys.executable}")
        print("   ✅ Python环境正常")
        
        print("\\n2. 测试PyQt5导入...")
        from PyQt5.QtWidgets import QApplication, QMainWindow, QLabel, QVBoxLayout, QWidget, QPushButton
        from PyQt5.QtCore import Qt
        from PyQt5.QtGui import QIcon, QPixmap, QPainter, QBrush, QColor
        print("   ✅ PyQt5导入成功")
        
        print("\\n3. 创建简单GUI窗口...")
        app = QApplication(sys.argv)
        
        window = QMainWindow()
        window.setWindowTitle("企业微信自动回复系统 - 基础测试")
        window.setGeometry(100, 100, 500, 400)
        
        # 创建默认图标
        try:
            pixmap = QPixmap(32, 32)
            pixmap.fill()
            
            painter = QPainter(pixmap)
            painter.setRenderHint(QPainter.Antialiasing)
            painter.setBrush(QBrush(QColor(0, 120, 215)))
            painter.drawEllipse(4, 4, 24, 24)
            painter.setPen(QColor(255, 255, 255))
            painter.drawText(12, 20, "W")
            painter.end()
            
            icon = QIcon(pixmap)
            window.setWindowIcon(icon)
            print("   ✅ 默认图标创建成功")
        except Exception as e:
            print(f"   ⚠️ 图标创建失败: {e}")
        
        # 创建内容
        central_widget = QWidget()
        window.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 标题
        title_label = QLabel("🎉 基础功能测试成功！")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("font-size: 20px; font-weight: bold; color: #2c3e50; margin: 20px;")
        layout.addWidget(title_label)
        
        # 状态信息
        status_label = QLabel(f"""
✅ Python环境: {sys.version.split()[0]}
✅ PyQt5: 正常加载和运行
✅ GUI界面: 正常显示
✅ 图标系统: 正常工作
✅ 打包环境: PyInstaller正常

🎯 如果您能看到这个窗口，说明：
   - PyInstaller打包成功
   - PyQt5环境正常
   - 基础GUI功能完全可用
   
💡 接下来可以测试完整版本的应用程序
""")
        status_label.setStyleSheet("font-size: 12px; color: #34495e; margin: 10px; padding: 10px; background-color: #f8f9fa; border-radius: 5px;")
        layout.addWidget(status_label)
        
        # 关闭按钮
        close_button = QPushButton("关闭测试")
        close_button.setStyleSheet("font-size: 14px; padding: 10px; background-color: #3498db; color: white; border: none; border-radius: 5px;")
        close_button.clicked.connect(window.close)
        layout.addWidget(close_button)
        
        print("\\n4. 显示测试窗口...")
        window.show()
        print("   ✅ 测试窗口已显示")
        
        print("\\n🎉 所有基础测试完成！")
        print("💡 请检查窗口是否正确显示")
        
        # 运行应用
        result = app.exec_()
        print(f"\\n✅ 应用程序正常退出，返回码: {result}")
        return result
        
    except Exception as e:
        print(f"\\n❌ 测试失败: {e}")
        traceback.print_exc()
        input("\\n按回车键退出...")
        return 1

if __name__ == "__main__":
    sys.exit(main())
'''
    
    with open('working_test_app.py', 'w', encoding='utf-8') as f:
        f.write(test_app_code)
    
    print("✅ 可工作的简单测试应用创建完成: working_test_app.py")

def create_working_test_spec():
    """创建可工作的测试应用spec文件"""
    print("📝 创建可工作的测试应用spec文件...")
    
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-
# 可工作的测试应用spec文件

import os
from pathlib import Path

project_root = os.path.dirname(os.path.abspath(SPEC))

# 最小化的数据文件
datas = []

# 隐藏导入 - 只包含必要的PyQt5
hiddenimports = [
    'PyQt5.QtCore', 
    'PyQt5.QtGui', 
    'PyQt5.QtWidgets', 
    'PyQt5.sip',
]

excludes = [
    'matplotlib', 'scipy', 'tensorflow', 'torch', 'tkinter', 'pygame', 'voice', 
    'ntwork', 'pilk', 'PIL', 'numpy', 'aiohttp', 'websockets'
]

block_cipher = None

a = Analysis(
    ['working_test_app.py'],
    pathex=[project_root],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=excludes,
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

icon_path = os.path.join(project_root, '2.ico')

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='基础功能测试',
    debug=True,
    bootloader_ignore_signals=False,
    strip=False,
    upx=False,
    console=True,  # 显示控制台
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=icon_path if os.path.exists(icon_path) else None,
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=False,
    upx_exclude=[],
    name='基础功能测试',
)
'''
    
    with open('working_test.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✅ 可工作的测试应用spec文件创建完成")

def create_fixed_ultimate_spec():
    """创建修复后的终极解决方案spec文件"""
    print("📝 创建修复后的终极解决方案spec文件...")
    
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-
# 修复后的终极解决方案 - 确保所有模块被正确包含

import os
from pathlib import Path

project_root = os.path.dirname(os.path.abspath(SPEC))

# 数据文件 - 包含配置文件
datas = []

# 配置文件
config_files = ['config-template.json', 'config-gui.json']
for config_file in config_files:
    config_path = os.path.join(project_root, config_file)
    if os.path.exists(config_path):
        datas.append((config_path, '.'))

# ntwork目录
ntwork_path = os.path.join(project_root, 'ntwork')
if os.path.exists(ntwork_path):
    for root, dirs, files in os.walk(ntwork_path):
        for file in files:
            if not file.endswith('.pyc') and '__pycache__' not in root:
                src_path = os.path.join(root, file)
                rel_path = os.path.relpath(src_path, project_root)
                dest_path = os.path.dirname(rel_path)
                datas.append((src_path, dest_path))

# 隐藏导入 - 确保包含所有必要模块
hiddenimports = [
    # PyQt5
    'PyQt5.QtCore', 'PyQt5.QtGui', 'PyQt5.QtWidgets', 'PyQt5.sip', 'PyQt5.QtPrintSupport',
    
    # PIL/Pillow
    'PIL', 'PIL.Image', 'PIL.ImageTk', 'PIL.ImageDraw', 'PIL.ImageFont', 'PIL.ImageFilter',
    'PIL.ImageEnhance', 'PIL.ImageOps', 'PIL.ImageChops', 'PIL.ImageStat', 'PIL.ImageColor',
    'PIL.ImageFile', 'PIL.ImageSequence', 'PIL.ImageShow', 'PIL.ImageWin', 'PIL.ImageGrab',
    'PIL.ImagePath', 'PIL.ImageQt', 'PIL.ImageCms', 'PIL.ImageMath', 'PIL.ImageMode',
    'PIL.ImagePalette', 'PIL.ImageTransform', 'PIL._imaging', 'PIL._imagingft',
    'PIL._imagingmath', 'PIL._imagingmorph', 'PIL._imagingcms', 'PIL._webp', 'PIL._tkinter_finder',
    
    # ntwork
    'ntwork', 'ntwork.core', 'ntwork.core.wework', 'ntwork.core.mgr', 'ntwork.utils',
    'ntwork.utils.logger', 'ntwork.utils.singleton', 'ntwork.utils.xdg', 'ntwork.const',
    'ntwork.const.notify_type', 'ntwork.const.send_type', 'ntwork.exception', 'ntwork.wc',
    
    # 依赖
    'pilk', 'pyee', 'pyee.base', 'pyee.asyncio', 'websockets', 'websockets.client',
    'websockets.server', 'aiohttp', 'aiohttp.client', 'aiohttp.web',
    
    # 应用模块 - 确保包含common目录下的所有模块
    'gui.main_window', 'gui.controllers', 'gui.config_loader', 'gui.managers',
    'gui.system_tray', 'gui.help_window', 'gui.donate_window', 'config',
    'common', 'common.log', 'common.utils', 'common.singleton', 'common.embedded_resources',
    'bridge.bridge', 'bridge.context', 'bridge.reply', 'bot.bot_factory',
    'channel.channel_factory', 'plugins.plugin_manager',
    
    # 其他
    'requests', 'json', 'threading', 'queue', 'sqlite3', 'configparser', 'logging',
    'ctypes', 'ctypes.wintypes', 'win32api', 'win32con', 'win32gui', 'pywintypes',
    'asyncio', 'typing_extensions', 'dataclasses', 'base64', 'tempfile', 'atexit',
]

excludes = ['matplotlib', 'scipy', 'tensorflow', 'torch', 'tkinter', 'pygame', 'voice']

block_cipher = None

a = Analysis(
    ['gui_app.py'],
    pathex=[project_root],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=excludes,
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

icon_path = os.path.join(project_root, '2.ico')

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='企业微信自动回复系统_修复完成版',
    debug=True,  # 启用调试
    bootloader_ignore_signals=False,
    strip=False,
    upx=False,
    console=True,  # 显示控制台用于调试
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=icon_path if os.path.exists(icon_path) else None,
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=False,
    upx_exclude=[],
    name='企业微信自动回复系统_修复完成版',
)
'''
    
    with open('fixed_ultimate.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✅ 修复后的终极解决方案spec文件创建完成")

def main():
    """主函数"""
    print("🔧 修复打包问题")
    print("=" * 50)
    
    # 创建可工作的简单测试
    create_working_simple_test()
    
    # 创建可工作的测试spec
    create_working_test_spec()
    
    # 创建修复后的终极spec
    create_fixed_ultimate_spec()
    
    print("\n" + "=" * 50)
    print("🎉 修复工具创建完成！")
    print("=" * 50)
    print("💡 测试步骤:")
    print("1. 先测试基础功能:")
    print("   pack_env\\Scripts\\python.exe -m PyInstaller working_test.spec")
    print("   python fix_qt_plugins.py")
    print("   运行: dist\\基础功能测试\\基础功能测试.exe")
    print("")
    print("2. 如果基础功能正常，再测试完整版本:")
    print("   pack_env\\Scripts\\python.exe -m PyInstaller fixed_ultimate.spec")
    print("   python fix_qt_plugins.py")
    print("   运行: dist\\企业微信自动回复系统_修复完成版\\企业微信自动回复系统_修复完成版.exe")
    print("")
    print("✅ 这次应该能正确工作！")

if __name__ == "__main__":
    main()
