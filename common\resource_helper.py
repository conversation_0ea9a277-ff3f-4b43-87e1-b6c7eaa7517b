#!/usr/bin/env python3
# encoding:utf-8
"""
资源路径解析辅助模块
解决PyInstaller打包后的路径问题
"""

import os
import sys
from pathlib import Path

def get_resource_path(relative_path):
    """
    获取资源文件的绝对路径
    
    Args:
        relative_path (str): 相对路径，如 "2.ico" 或 "12.jpg"
    
    Returns:
        str: 资源文件的绝对路径
    """
    try:
        # PyInstaller打包后的情况
        if hasattr(sys, '_MEIPASS'):
            # 在临时目录中查找
            base_path = sys._MEIPASS
            resource_path = os.path.join(base_path, relative_path)
            if os.path.exists(resource_path):
                return resource_path
        
        # 开发环境或其他情况，尝试多个可能的路径
        possible_paths = [
            # 当前工作目录
            os.path.join(os.getcwd(), relative_path),
            
            # exe文件所在目录
            os.path.join(os.path.dirname(sys.executable), relative_path),
            
            # 脚本文件所在目录的上级目录（开发环境）
            os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), relative_path),
            
            # 脚本文件所在目录
            os.path.join(os.path.dirname(os.path.abspath(__file__)), relative_path),
        ]
        
        # 尝试每个可能的路径
        for path in possible_paths:
            if os.path.exists(path):
                return path
        
        # 如果都找不到，返回相对路径（让调用者处理）
        return relative_path
        
    except Exception as e:
        print(f"获取资源路径失败: {e}")
        return relative_path

def get_icon_path():
    """获取程序图标路径"""
    return get_resource_path("2.ico")

def get_contact_image_path():
    """获取联系方式图片路径"""
    return get_resource_path("12.jpg")

def get_donate_image_path():
    """获取捐赠二维码路径"""
    return get_resource_path("33.png")

def debug_paths():
    """调试路径信息"""
    print("🔍 路径调试信息:")
    print(f"   当前工作目录: {os.getcwd()}")
    print(f"   sys.executable: {sys.executable}")
    print(f"   __file__: {__file__ if '__file__' in globals() else '未定义'}")
    
    if hasattr(sys, '_MEIPASS'):
        print(f"   PyInstaller临时目录: {sys._MEIPASS}")
    
    print("🔍 资源文件路径:")
    print(f"   图标文件: {get_icon_path()}")
    print(f"   联系方式图片: {get_contact_image_path()}")
    print(f"   捐赠二维码: {get_donate_image_path()}")
    
    print("🔍 文件存在性检查:")
    for name, path in [
        ("图标文件", get_icon_path()),
        ("联系方式图片", get_contact_image_path()),
        ("捐赠二维码", get_donate_image_path())
    ]:
        exists = os.path.exists(path)
        print(f"   {name}: {'✅' if exists else '❌'} {path}")

if __name__ == "__main__":
    debug_paths()
