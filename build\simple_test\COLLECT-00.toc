([('简单测试应用.exe',
   'E:\\Desktop\\企业微信\\build\\simple_test\\简单测试应用.exe',
   'EXECUTABLE'),
  ('VCRUNTIME140.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\VCRUNTIME140.dll',
   'BINARY'),
  ('python310.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\python310.dll',
   'BINARY'),
  ('select',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\select.pyd',
   'EXTENSION'),
  ('_decimal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_hashlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('_socket',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('_lzma',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\_lzma.pyd',
   'EXTENSION'),
  ('_bz2',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\_bz2.pyd',
   'EXTENSION'),
  ('PyQt5.sip',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\PyQt5\\sip.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('unicodedata',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('_ssl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\_ssl.pyd',
   'EXTENSION'),
  ('PyQt5.QtGui',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\PyQt5\\QtGui.pyd',
   'EXTENSION'),
  ('PyQt5.QtCore',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\PyQt5\\QtCore.pyd',
   'EXTENSION'),
  ('PyQt5.QtWidgets',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\PyQt5\\QtWidgets.pyd',
   'EXTENSION'),
  ('libcrypto-1_1.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\libcrypto-1_1.dll',
   'BINARY'),
  ('libssl-1_1.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\libssl-1_1.dll',
   'BINARY'),
  ('python3.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\python3.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Gui.dll',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Gui.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Core.dll',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Core.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Widgets.dll',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Widgets.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\MSVCP140.dll',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\PyQt5\\Qt5\\bin\\MSVCP140.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\VCRUNTIME140_1.dll',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\PyQt5\\Qt5\\bin\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\MSVCP140_1.dll',
   'E:\\Desktop\\企业微信\\pack_env\\lib\\site-packages\\PyQt5\\Qt5\\bin\\MSVCP140_1.dll',
   'BINARY'),
  ('base_library.zip',
   'E:\\Desktop\\企业微信\\build\\simple_test\\base_library.zip',
   'DATA')],)
