#!/usr/bin/env python3
# encoding:utf-8
"""
调试和修复图片显示问题的最终方案
"""

import os
import sys
import shutil
from pathlib import Path

def create_debug_version():
    """创建带调试信息的控制台版本"""
    print("🔧 创建调试版本...")
    
    debug_spec = '''# -*- mode: python ; coding: utf-8 -*-

import os
from pathlib import Path

# 项目根目录
project_root = os.path.dirname(os.path.abspath(SPEC))

# 数据文件 - 确保图片文件在正确位置
datas = []

# 配置文件
config_files = ['config-template.json', 'config-gui.json']
for config_file in config_files:
    config_path = os.path.join(project_root, config_file)
    if os.path.exists(config_path):
        datas.append((config_path, '.'))

# 图片文件 - 多个位置确保能找到
image_files = ['2.ico', '12.jpg', '33.png']
for image_file in image_files:
    image_path = os.path.join(project_root, image_file)
    if os.path.exists(image_path):
        # 放在根目录
        datas.append((image_path, '.'))
        # 也放在images目录
        datas.append((image_path, 'images'))
        print(f"添加图片文件: {image_file} -> 根目录和images目录")

# ntwork目录
ntwork_path = os.path.join(project_root, 'ntwork')
if os.path.exists(ntwork_path):
    for root, dirs, files in os.walk(ntwork_path):
        for file in files:
            if not file.endswith('.pyc') and '__pycache__' not in root:
                src_path = os.path.join(root, file)
                rel_path = os.path.relpath(src_path, project_root)
                dest_path = os.path.dirname(rel_path)
                datas.append((src_path, dest_path))

# 隐藏导入
hiddenimports = [
    'PyQt5.QtCore', 'PyQt5.QtGui', 'PyQt5.QtWidgets', 'PyQt5.sip', 'PyQt5.QtPrintSupport',
    'PIL', 'PIL.Image', 'PIL.ImageTk', 'PIL.ImageDraw', 'PIL.ImageFont', 'PIL.ImageFilter',
    'PIL.ImageEnhance', 'PIL.ImageOps', 'PIL.ImageChops', 'PIL.ImageStat', 'PIL.ImageColor',
    'PIL.ImageFile', 'PIL.ImageSequence', 'PIL.ImageShow', 'PIL.ImageWin', 'PIL.ImageGrab',
    'PIL.ImagePath', 'PIL.ImageQt', 'PIL.ImageCms', 'PIL.ImageMath', 'PIL.ImageMode',
    'PIL.ImagePalette', 'PIL.ImageTransform', 'PIL._imaging', 'PIL._imagingft',
    'PIL._imagingmath', 'PIL._imagingmorph', 'PIL._imagingcms', 'PIL._webp', 'PIL._tkinter_finder',
    'ntwork', 'ntwork.core', 'ntwork.core.wework', 'ntwork.core.mgr', 'ntwork.utils',
    'ntwork.utils.logger', 'ntwork.utils.singleton', 'ntwork.utils.xdg', 'ntwork.const',
    'ntwork.const.notify_type', 'ntwork.const.send_type', 'ntwork.exception', 'ntwork.wc',
    'pilk', 'pyee', 'pyee.base', 'pyee.asyncio', 'websockets', 'websockets.client',
    'websockets.server', 'aiohttp', 'aiohttp.client', 'aiohttp.web',
    'gui.main_window', 'gui.controllers', 'gui.config_loader', 'gui.managers',
    'gui.system_tray', 'gui.help_window', 'gui.donate_window', 'config',
    'common.log', 'common.utils', 'common.singleton', 'common.resource_manager',
    'bridge.bridge', 'bridge.context', 'bridge.reply', 'bot.bot_factory',
    'channel.channel_factory', 'plugins.plugin_manager',
    'requests', 'json', 'threading', 'queue', 'sqlite3', 'configparser', 'logging',
    'ctypes', 'ctypes.wintypes', 'win32api', 'win32con', 'win32gui', 'pywintypes',
    'asyncio', 'typing_extensions', 'dataclasses',
]

excludes = ['matplotlib', 'scipy', 'tensorflow', 'torch', 'tkinter', 'pygame', 'voice']

block_cipher = None

a = Analysis(
    ['gui_app.py'],
    pathex=[project_root],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=excludes,
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

icon_path = os.path.join(project_root, '2.ico')

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='企业微信自动回复系统_调试版',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=False,
    console=True,  # 显示控制台用于调试
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=icon_path if os.path.exists(icon_path) else None,
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=False,
    upx_exclude=[],
    name='企业微信自动回复系统_调试版',
)
'''
    
    with open('debug_pack.spec', 'w', encoding='utf-8') as f:
        f.write(debug_spec)
    
    print("✅ 调试版spec文件创建完成")

def enhance_resource_manager():
    """增强资源管理器，添加更多调试信息"""
    print("🔧 增强资源管理器...")
    
    enhanced_code = '''#!/usr/bin/env python3
# encoding:utf-8
"""
增强的资源管理器 - 带详细调试信息
"""

import os
import sys
from pathlib import Path

class EnhancedResourceManager:
    """增强的资源管理器类"""
    
    @staticmethod
    def debug_environment():
        """调试环境信息"""
        print("🔍 环境调试信息:")
        print(f"   sys.frozen: {getattr(sys, 'frozen', False)}")
        print(f"   sys._MEIPASS: {getattr(sys, '_MEIPASS', '未定义')}")
        print(f"   sys.executable: {sys.executable}")
        print(f"   os.getcwd(): {os.getcwd()}")
        print(f"   __file__: {__file__ if '__file__' in globals() else '未定义'}")
        
        if hasattr(sys, '_MEIPASS'):
            print(f"   PyInstaller临时目录内容:")
            temp_dir = Path(sys._MEIPASS)
            if temp_dir.exists():
                for item in temp_dir.iterdir():
                    if item.name in ['2.ico', '12.jpg', '33.png']:
                        print(f"     ✅ {item.name}: {item.stat().st_size} 字节")
    
    @staticmethod
    def get_all_possible_paths(filename):
        """获取所有可能的文件路径"""
        paths = []
        
        # PyInstaller临时目录
        if hasattr(sys, '_MEIPASS'):
            paths.append(os.path.join(sys._MEIPASS, filename))
            paths.append(os.path.join(sys._MEIPASS, 'images', filename))
        
        # exe文件所在目录
        exe_dir = os.path.dirname(sys.executable)
        paths.append(os.path.join(exe_dir, filename))
        paths.append(os.path.join(exe_dir, 'images', filename))
        
        # 当前工作目录
        paths.append(os.path.join(os.getcwd(), filename))
        paths.append(os.path.join(os.getcwd(), 'images', filename))
        
        # 脚本文件目录（开发环境）
        if '__file__' in globals():
            script_dir = os.path.dirname(os.path.abspath(__file__))
            paths.append(os.path.join(script_dir, filename))
            paths.append(os.path.join(os.path.dirname(script_dir), filename))
        
        return paths
    
    @staticmethod
    def get_resource_path(filename):
        """获取资源文件路径，带详细调试"""
        print(f"🔍 查找文件: {filename}")
        
        paths = EnhancedResourceManager.get_all_possible_paths(filename)
        
        for i, path in enumerate(paths):
            exists = os.path.exists(path)
            print(f"   路径{i+1}: {'✅' if exists else '❌'} {path}")
            if exists:
                size = os.path.getsize(path)
                print(f"          文件大小: {size} 字节")
                return path
        
        print(f"   ❌ 所有路径都未找到文件: {filename}")
        return paths[0] if paths else filename
    
    @staticmethod
    def get_icon_path():
        """获取程序图标路径"""
        return EnhancedResourceManager.get_resource_path("2.ico")
    
    @staticmethod
    def get_contact_image_path():
        """获取联系方式图片路径"""
        return EnhancedResourceManager.get_resource_path("12.jpg")
    
    @staticmethod
    def get_donate_image_path():
        """获取捐赠二维码路径"""
        return EnhancedResourceManager.get_resource_path("33.png")

# 便捷函数
def get_icon_path():
    return EnhancedResourceManager.get_icon_path()

def get_contact_image_path():
    return EnhancedResourceManager.get_contact_image_path()

def get_donate_image_path():
    return EnhancedResourceManager.get_donate_image_path()

def debug_resources():
    """调试所有资源"""
    EnhancedResourceManager.debug_environment()
    print("\\n🔍 资源文件检查:")
    get_icon_path()
    get_contact_image_path()
    get_donate_image_path()

if __name__ == "__main__":
    debug_resources()
'''
    
    # 覆盖原来的资源管理器
    common_dir = Path("common")
    if not common_dir.exists():
        common_dir.mkdir()
    
    resource_manager_path = common_dir / "resource_manager.py"
    with open(resource_manager_path, 'w', encoding='utf-8') as f:
        f.write(enhanced_code)
    
    print("✅ 增强资源管理器创建完成")

def copy_images_to_multiple_locations():
    """将图片复制到多个位置确保能找到"""
    print("📁 复制图片到多个位置...")
    
    # 创建images目录
    images_dir = Path("images")
    if not images_dir.exists():
        images_dir.mkdir()
    
    image_files = ["2.ico", "12.jpg", "33.png"]
    
    for image_file in image_files:
        if Path(image_file).exists():
            # 复制到images目录
            shutil.copy2(image_file, images_dir / image_file)
            print(f"✅ 复制 {image_file} 到 images/ 目录")
        else:
            print(f"❌ 源文件不存在: {image_file}")

def create_test_script():
    """创建测试脚本"""
    print("🧪 创建测试脚本...")
    
    test_script = '''#!/usr/bin/env python3
# encoding:utf-8
"""
图片显示测试脚本
"""

import sys
import os

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from common.resource_manager import debug_resources

def main():
    print("🧪 图片显示测试脚本")
    print("=" * 50)
    
    # 调试资源
    debug_resources()
    
    print("\\n" + "=" * 50)
    print("测试完成，请查看上面的调试信息")
    input("按回车键退出...")

if __name__ == "__main__":
    main()
'''
    
    with open("test_images.py", 'w', encoding='utf-8') as f:
        f.write(test_script)
    
    print("✅ 测试脚本创建完成: test_images.py")

def main():
    """主函数"""
    print("🔧 调试和修复图片显示问题")
    print("=" * 50)
    
    # 创建调试版本
    create_debug_version()
    
    # 增强资源管理器
    enhance_resource_manager()
    
    # 复制图片到多个位置
    copy_images_to_multiple_locations()
    
    # 创建测试脚本
    create_test_script()
    
    print("\n" + "=" * 50)
    print("🎉 调试工具创建完成！")
    print("=" * 50)
    print("💡 调试步骤:")
    print("1. 运行: pack_env\\Scripts\\python.exe -m PyInstaller debug_pack.spec")
    print("2. 运行: python fix_qt_plugins.py")
    print("3. 测试: cd dist\\企业微信自动回复系统_调试版 && test_images.py")
    print("4. 运行调试版exe，查看控制台输出")
    print("\n✅ 这次我们能看到详细的调试信息，找出问题所在！")

if __name__ == "__main__":
    main()
