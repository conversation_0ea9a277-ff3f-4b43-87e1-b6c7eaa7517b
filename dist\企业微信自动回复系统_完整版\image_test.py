#!/usr/bin/env python3
# encoding:utf-8
"""
图片显示测试脚本
"""

import os
import sys
from pathlib import Path
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel
from PyQt5.QtGui import QPixmap, QIcon
from PyQt5.QtCore import Qt

class ImageTestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("图片显示测试")
        self.setGeometry(100, 100, 800, 600)
        
        # 设置窗口图标
        self.set_window_icon()
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 测试图标文件
        self.test_icon(layout)
        
        # 测试12.jpg
        self.test_image(layout, "12.jpg", "联系方式图片")
        
        # 测试33.png
        self.test_image(layout, "33.png", "捐赠二维码")
    
    def set_window_icon(self):
        """设置窗口图标"""
        try:
            current_dir = Path(__file__).parent
            icon_path = current_dir / "2.ico"
            
            if icon_path.exists():
                icon = QIcon(str(icon_path))
                self.setWindowIcon(icon)
                print(f"✅ 窗口图标设置成功: {icon_path}")
            else:
                print(f"❌ 图标文件不存在: {icon_path}")
        except Exception as e:
            print(f"❌ 设置窗口图标失败: {e}")
    
    def test_image(self, layout, filename, description):
        """测试图片显示"""
        label = QLabel()
        label.setAlignment(Qt.AlignCenter)
        label.setStyleSheet("border: 1px solid #ccc; margin: 10px; padding: 10px;")
        
        try:
            current_dir = Path(__file__).parent
            image_path = current_dir / filename
            
            if image_path.exists():
                pixmap = QPixmap(str(image_path))
                if not pixmap.isNull():
                    # 缩放图片
                    scaled_pixmap = pixmap.scaled(200, 200, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                    label.setPixmap(scaled_pixmap)
                    label.setText(f"✅ {description}\n({filename})")
                    print(f"✅ {filename} 显示成功")
                else:
                    label.setText(f"❌ {description}\n({filename} 无法加载)")
                    print(f"❌ {filename} 无法加载")
            else:
                label.setText(f"❌ {description}\n({filename} 文件不存在)")
                print(f"❌ {filename} 文件不存在: {image_path}")
        except Exception as e:
            label.setText(f"❌ {description}\n(加载失败: {str(e)})")
            print(f"❌ {filename} 加载失败: {e}")
        
        layout.addWidget(label)

def main():
    """主函数"""
    print("🧪 图片显示测试程序")
    print("=" * 30)
    
    # 检查当前目录的图片文件
    current_dir = Path(__file__).parent
    image_files = ["2.ico", "12.jpg", "33.png"]
    
    print("📁 当前目录图片文件检查:")
    for image_file in image_files:
        image_path = current_dir / image_file
        if image_path.exists():
            size = image_path.stat().st_size
            print(f"  ✅ {image_file}: {size} 字节")
        else:
            print(f"  ❌ {image_file}: 不存在")
    
    print("\n🖼️ 启动图片显示测试窗口...")
    
    app = QApplication(sys.argv)
    window = ImageTestWindow()
    window.show()
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
