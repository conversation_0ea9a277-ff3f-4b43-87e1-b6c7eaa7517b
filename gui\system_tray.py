# encoding:utf-8
"""
企业微信自动回复系统 - 系统托盘功能
"""

from PyQt5.QtWidgets import QSystemTrayIcon, QMenu, QAction, QApplication
from PyQt5.QtCore import QObject, pyqtSignal
from common.simple_resource_manager import get_icon_path
from common.resource_helper import get_icon_path
from PyQt5.QtGui import QIcon, QPixmap, QPainter, QBrush, QColor
import os


class SystemTray(QObject):
    """系统托盘类"""

    # 定义信号
    show_window = pyqtSignal()
    hide_window = pyqtSignal()
    start_service = pyqtSignal()
    stop_service = pyqtSignal()
    quit_application = pyqtSignal()

    def __init__(self, main_window=None):
        super().__init__()
        self.main_window = main_window
        self.tray_icon = None
        self.tray_menu = None
        self.is_service_running = False
        self.init_tray()

    def init_tray(self):
        """初始化系统托盘"""
        # 检查系统是否支持托盘
        if not QSystemTrayIcon.isSystemTrayAvailable():
            print("系统不支持托盘功能")
            return False

        # 创建托盘图标
        self.tray_icon = QSystemTrayIcon()
        self.tray_icon.setIcon(self.create_tray_icon())
        self.tray_icon.setToolTip("企业微信自动回复系统")

        # 创建托盘菜单
        self.create_tray_menu()

        # 连接信号
        self.tray_icon.activated.connect(self.on_tray_activated)

        # 显示托盘图标
        self.tray_icon.show()

        return True

    def create_tray_icon(self):
        """创建托盘图标"""
        try:
            # 尝试使用2.ico文件
            icon_path = get_icon_path()
            print(f"尝试加载系统托盘图标: {icon_path}")
            if os.path.exists(icon_path):
                icon = QIcon(icon_path)
                print(f"系统托盘图标设置成功: {icon_path}")
                return icon
            else:
                print(f"图标文件不存在: {icon_path}")
        except Exception as e:
            print(f"加载图标文件失败: {e}")

        # 如果加载失败，创建一个简单的图标
        pixmap = QPixmap(16, 16)
        pixmap.fill(QColor(0, 0, 0, 0))  # 透明背景

        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.Antialiasing)

        # 绘制一个简单的圆形图标
        painter.setBrush(QBrush(QColor(0, 120, 215)))  # 蓝色
        painter.drawEllipse(2, 2, 12, 12)

        # 绘制字母"W"
        painter.setPen(QColor(255, 255, 255))
        painter.drawText(5, 11, "W")

        painter.end()

        return QIcon(pixmap)

    def create_tray_menu(self):
        """创建托盘右键菜单"""
        self.tray_menu = QMenu()

        # 显示/隐藏窗口
        self.show_action = QAction("显示窗口", self)
        self.show_action.triggered.connect(self.on_show_window)
        self.tray_menu.addAction(self.show_action)

        self.hide_action = QAction("隐藏窗口", self)
        self.hide_action.triggered.connect(self.on_hide_window)
        self.tray_menu.addAction(self.hide_action)

        self.tray_menu.addSeparator()

        # 服务控制
        self.start_action = QAction("启动服务", self)
        self.start_action.triggered.connect(self.on_start_service)
        self.tray_menu.addAction(self.start_action)

        self.stop_action = QAction("停止服务", self)
        self.stop_action.triggered.connect(self.on_stop_service)
        self.stop_action.setEnabled(False)
        self.tray_menu.addAction(self.stop_action)

        self.tray_menu.addSeparator()

        # 退出应用
        quit_action = QAction("退出", self)
        quit_action.triggered.connect(self.on_quit_application)
        self.tray_menu.addAction(quit_action)

        # 设置菜单
        self.tray_icon.setContextMenu(self.tray_menu)

    def on_tray_activated(self, reason):
        """托盘图标激活事件"""
        if reason == QSystemTrayIcon.DoubleClick:
            # 双击显示/隐藏窗口
            if self.main_window and self.main_window.isVisible():
                self.on_hide_window()
            else:
                self.on_show_window()

    def on_show_window(self):
        """显示窗口"""
        self.show_window.emit()
        if self.main_window:
            self.main_window.show()
            self.main_window.raise_()
            self.main_window.activateWindow()

    def on_hide_window(self):
        """隐藏窗口"""
        self.hide_window.emit()
        if self.main_window:
            self.main_window.hide()

    def on_start_service(self):
        """启动服务"""
        self.start_service.emit()

    def on_stop_service(self):
        """停止服务"""
        self.stop_service.emit()

    def on_quit_application(self):
        """退出应用"""
        self.quit_application.emit()

    def update_service_status(self, is_running):
        """更新服务状态"""
        self.is_service_running = is_running

        if is_running:
            self.start_action.setEnabled(False)
            self.stop_action.setEnabled(True)
            self.tray_icon.setToolTip("企业微信自动回复系统 - 运行中")
        else:
            self.start_action.setEnabled(True)
            self.stop_action.setEnabled(False)
            self.tray_icon.setToolTip("企业微信自动回复系统 - 已停止")

    def show_message(self, title, message, icon=QSystemTrayIcon.Information):
        """显示托盘消息"""
        if self.tray_icon:
            self.tray_icon.showMessage(title, message, icon, 3000)

    def hide_tray(self):
        """隐藏托盘图标"""
        if self.tray_icon:
            self.tray_icon.hide()
