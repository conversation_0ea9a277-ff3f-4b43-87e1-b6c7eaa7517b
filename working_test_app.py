#!/usr/bin/env python3
# encoding:utf-8
"""
可工作的简单测试应用 - 不依赖嵌入式资源
"""

import sys
import os
import traceback

def main():
    """主函数"""
    print("🧪 企业微信自动回复系统 - 基础功能测试")
    print("=" * 50)
    
    try:
        print("1. 测试Python基础环境...")
        print(f"   Python版本: {sys.version}")
        print(f"   当前目录: {os.getcwd()}")
        print(f"   可执行文件: {sys.executable}")
        print("   ✅ Python环境正常")
        
        print("\n2. 测试PyQt5导入...")
        from PyQt5.QtWidgets import QApplication, QMainWindow, QLabel, QVBoxLayout, QWidget, QPushButton
        from PyQt5.QtCore import Qt
        from PyQt5.QtGui import QIcon, QPixmap, QPainter, QBrush, QColor
        print("   ✅ PyQt5导入成功")
        
        print("\n3. 创建简单GUI窗口...")
        app = QApplication(sys.argv)
        
        window = QMainWindow()
        window.setWindowTitle("企业微信自动回复系统 - 基础测试")
        window.setGeometry(100, 100, 500, 400)
        
        # 创建默认图标
        try:
            pixmap = QPixmap(32, 32)
            pixmap.fill()
            
            painter = QPainter(pixmap)
            painter.setRenderHint(QPainter.Antialiasing)
            painter.setBrush(QBrush(QColor(0, 120, 215)))
            painter.drawEllipse(4, 4, 24, 24)
            painter.setPen(QColor(255, 255, 255))
            painter.drawText(12, 20, "W")
            painter.end()
            
            icon = QIcon(pixmap)
            window.setWindowIcon(icon)
            print("   ✅ 默认图标创建成功")
        except Exception as e:
            print(f"   ⚠️ 图标创建失败: {e}")
        
        # 创建内容
        central_widget = QWidget()
        window.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 标题
        title_label = QLabel("🎉 基础功能测试成功！")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("font-size: 20px; font-weight: bold; color: #2c3e50; margin: 20px;")
        layout.addWidget(title_label)
        
        # 状态信息
        status_label = QLabel(f"""
✅ Python环境: {sys.version.split()[0]}
✅ PyQt5: 正常加载和运行
✅ GUI界面: 正常显示
✅ 图标系统: 正常工作
✅ 打包环境: PyInstaller正常

🎯 如果您能看到这个窗口，说明：
   - PyInstaller打包成功
   - PyQt5环境正常
   - 基础GUI功能完全可用
   
💡 接下来可以测试完整版本的应用程序
""")
        status_label.setStyleSheet("font-size: 12px; color: #34495e; margin: 10px; padding: 10px; background-color: #f8f9fa; border-radius: 5px;")
        layout.addWidget(status_label)
        
        # 关闭按钮
        close_button = QPushButton("关闭测试")
        close_button.setStyleSheet("font-size: 14px; padding: 10px; background-color: #3498db; color: white; border: none; border-radius: 5px;")
        close_button.clicked.connect(window.close)
        layout.addWidget(close_button)
        
        print("\n4. 显示测试窗口...")
        window.show()
        print("   ✅ 测试窗口已显示")
        
        print("\n🎉 所有基础测试完成！")
        print("💡 请检查窗口是否正确显示")
        
        # 运行应用
        result = app.exec_()
        print(f"\n✅ 应用程序正常退出，返回码: {result}")
        return result
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        traceback.print_exc()
        input("\n按回车键退出...")
        return 1

if __name__ == "__main__":
    sys.exit(main())
