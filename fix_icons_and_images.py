#!/usr/bin/env python3
# encoding:utf-8
"""
修复图标和图片显示问题
"""

import os
import sys
import shutil
from pathlib import Path

def copy_images_to_dist():
    """复制图片文件到所有dist目录"""
    print("🖼️ 复制图片文件到dist目录...")
    
    # 需要复制的图片文件
    image_files = [
        "2.ico",      # 程序图标
        "12.jpg",     # 联系方式图片
        "33.png"      # 捐赠二维码图片
    ]
    
    # 检查源文件是否存在
    missing_files = []
    for image_file in image_files:
        if not Path(image_file).exists():
            missing_files.append(image_file)
    
    if missing_files:
        print(f"❌ 缺少源文件: {missing_files}")
        return False
    
    # 查找所有dist目录
    dist_dirs = []
    dist_base = Path("dist")
    if dist_base.exists():
        for item in dist_base.iterdir():
            if item.is_dir():
                dist_dirs.append(item)
    
    if not dist_dirs:
        print("❌ 未找到dist目录")
        return False
    
    success_count = 0
    for dist_dir in dist_dirs:
        print(f"📁 处理目录: {dist_dir}")
        
        try:
            for image_file in image_files:
                src = Path(image_file)
                dst = dist_dir / image_file
                
                shutil.copy2(src, dst)
                print(f"  ✅ 复制 {image_file} -> {dst}")
            
            success_count += 1
            
        except Exception as e:
            print(f"  ❌ 复制失败: {e}")
    
    print(f"📊 成功处理 {success_count}/{len(dist_dirs)} 个目录")
    return success_count > 0

def fix_icon_paths_in_exe():
    """修复exe文件中的图标路径问题"""
    print("🔧 修复图标路径问题...")
    
    # 查找所有exe文件
    exe_files = []
    dist_base = Path("dist")
    if dist_base.exists():
        for dist_dir in dist_base.iterdir():
            if dist_dir.is_dir():
                for exe_file in dist_dir.glob("*.exe"):
                    exe_files.append(exe_file)
    
    if not exe_files:
        print("❌ 未找到exe文件")
        return False
    
    print(f"📋 找到 {len(exe_files)} 个exe文件")
    
    # 为每个exe文件创建图标快捷方式
    for exe_file in exe_files:
        dist_dir = exe_file.parent
        
        # 检查图标文件是否存在
        icon_file = dist_dir / "2.ico"
        if icon_file.exists():
            print(f"  ✅ {exe_file.name}: 图标文件存在")
        else:
            print(f"  ❌ {exe_file.name}: 图标文件缺失")
    
    return True

def create_image_test_script():
    """创建图片测试脚本"""
    print("🧪 创建图片测试脚本...")
    
    test_script_content = '''#!/usr/bin/env python3
# encoding:utf-8
"""
图片显示测试脚本
"""

import os
import sys
from pathlib import Path
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel
from PyQt5.QtGui import QPixmap, QIcon
from PyQt5.QtCore import Qt

class ImageTestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("图片显示测试")
        self.setGeometry(100, 100, 800, 600)
        
        # 设置窗口图标
        self.set_window_icon()
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 测试图标文件
        self.test_icon(layout)
        
        # 测试12.jpg
        self.test_image(layout, "12.jpg", "联系方式图片")
        
        # 测试33.png
        self.test_image(layout, "33.png", "捐赠二维码")
    
    def set_window_icon(self):
        """设置窗口图标"""
        try:
            current_dir = Path(__file__).parent
            icon_path = current_dir / "2.ico"
            
            if icon_path.exists():
                icon = QIcon(str(icon_path))
                self.setWindowIcon(icon)
                print(f"✅ 窗口图标设置成功: {icon_path}")
            else:
                print(f"❌ 图标文件不存在: {icon_path}")
        except Exception as e:
            print(f"❌ 设置窗口图标失败: {e}")
    
    def test_image(self, layout, filename, description):
        """测试图片显示"""
        label = QLabel()
        label.setAlignment(Qt.AlignCenter)
        label.setStyleSheet("border: 1px solid #ccc; margin: 10px; padding: 10px;")
        
        try:
            current_dir = Path(__file__).parent
            image_path = current_dir / filename
            
            if image_path.exists():
                pixmap = QPixmap(str(image_path))
                if not pixmap.isNull():
                    # 缩放图片
                    scaled_pixmap = pixmap.scaled(200, 200, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                    label.setPixmap(scaled_pixmap)
                    label.setText(f"✅ {description}\\n({filename})")
                    print(f"✅ {filename} 显示成功")
                else:
                    label.setText(f"❌ {description}\\n({filename} 无法加载)")
                    print(f"❌ {filename} 无法加载")
            else:
                label.setText(f"❌ {description}\\n({filename} 文件不存在)")
                print(f"❌ {filename} 文件不存在: {image_path}")
        except Exception as e:
            label.setText(f"❌ {description}\\n(加载失败: {str(e)})")
            print(f"❌ {filename} 加载失败: {e}")
        
        layout.addWidget(label)

def main():
    """主函数"""
    print("🧪 图片显示测试程序")
    print("=" * 30)
    
    # 检查当前目录的图片文件
    current_dir = Path(__file__).parent
    image_files = ["2.ico", "12.jpg", "33.png"]
    
    print("📁 当前目录图片文件检查:")
    for image_file in image_files:
        image_path = current_dir / image_file
        if image_path.exists():
            size = image_path.stat().st_size
            print(f"  ✅ {image_file}: {size} 字节")
        else:
            print(f"  ❌ {image_file}: 不存在")
    
    print("\\n🖼️ 启动图片显示测试窗口...")
    
    app = QApplication(sys.argv)
    window = ImageTestWindow()
    window.show()
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
'''
    
    # 为每个dist目录创建测试脚本
    dist_base = Path("dist")
    if dist_base.exists():
        for dist_dir in dist_base.iterdir():
            if dist_dir.is_dir():
                test_script_path = dist_dir / "image_test.py"
                with open(test_script_path, 'w', encoding='utf-8') as f:
                    f.write(test_script_content)
                print(f"✅ 创建测试脚本: {test_script_path}")

def create_enhanced_spec_with_images():
    """创建包含图片的增强spec文件"""
    print("📝 创建包含图片的增强spec文件...")
    
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

import os
from pathlib import Path

# 项目根目录
project_root = os.path.dirname(os.path.abspath(SPEC))

# 数据文件 - 包含所有图片和配置文件
datas = [
    # 配置文件
    (os.path.join(project_root, 'config-template.json'), '.'),
    (os.path.join(project_root, 'config-gui.json'), '.'),
    
    # 图片文件
    (os.path.join(project_root, '2.ico'), '.'),
    (os.path.join(project_root, '12.jpg'), '.'),
    (os.path.join(project_root, '33.png'), '.'),
]

# 手动添加ntwork目录
ntwork_path = os.path.join(project_root, 'ntwork')
if os.path.exists(ntwork_path):
    for root, dirs, files in os.walk(ntwork_path):
        for file in files:
            if not file.endswith('.pyc') and '__pycache__' not in root:
                src_path = os.path.join(root, file)
                rel_path = os.path.relpath(src_path, project_root)
                dest_path = os.path.dirname(rel_path)
                datas.append((src_path, dest_path))

# 包含所有必要的隐藏导入
hiddenimports = [
    # PyQt5相关
    'PyQt5.QtCore',
    'PyQt5.QtGui', 
    'PyQt5.QtWidgets',
    'PyQt5.sip',
    'PyQt5.QtPrintSupport',
    
    # PIL/Pillow相关
    'PIL',
    'PIL.Image',
    'PIL.ImageTk',
    'PIL.ImageDraw',
    'PIL.ImageFont',
    'PIL.ImageFilter',
    'PIL.ImageEnhance',
    'PIL.ImageOps',
    'PIL.ImageChops',
    'PIL.ImageStat',
    'PIL.ImageColor',
    'PIL.ImageFile',
    'PIL.ImageSequence',
    'PIL.ImageShow',
    'PIL.ImageWin',
    'PIL.ImageGrab',
    'PIL.ImagePath',
    'PIL.ImageQt',
    'PIL.ImageCms',
    'PIL.ImageMath',
    'PIL.ImageMode',
    'PIL.ImagePalette',
    'PIL.ImageTransform',
    'PIL._imaging',
    'PIL._imagingft',
    'PIL._imagingmath',
    'PIL._imagingmorph',
    'PIL._imagingcms',
    'PIL._webp',
    'PIL._tkinter_finder',
    
    # ntwork相关
    'ntwork',
    'ntwork.core',
    'ntwork.core.wework',
    'ntwork.core.mgr',
    'ntwork.utils',
    'ntwork.utils.logger',
    'ntwork.utils.singleton',
    'ntwork.utils.xdg',
    'ntwork.const',
    'ntwork.const.notify_type',
    'ntwork.const.send_type',
    'ntwork.exception',
    'ntwork.wc',
    
    # ntwork依赖
    'pilk',
    'pyee',
    'pyee.base',
    'pyee.asyncio',
    'websockets',
    'websockets.client',
    'websockets.server',
    'aiohttp',
    'aiohttp.client',
    'aiohttp.web',
    
    # 应用程序模块
    'gui.main_window',
    'gui.controllers',
    'gui.config_loader',
    'gui.managers',
    'gui.system_tray',
    'gui.help_window',
    'gui.donate_window',
    'config',
    'common.log',
    'common.utils',
    'common.singleton',
    'bridge.bridge',
    'bridge.context',
    'bridge.reply',
    'bot.bot_factory',
    'channel.channel_factory',
    'plugins.plugin_manager',
    
    # 其他依赖
    'requests',
    'json',
    'threading',
    'queue',
    'sqlite3',
    'configparser',
    'logging',
    'ctypes',
    'ctypes.wintypes',
    'win32api',
    'win32con',
    'win32gui',
    'pywintypes',
    'asyncio',
    'typing_extensions',
    'dataclasses',
]

# 排除模块
excludes = [
    'matplotlib',
    'scipy',
    'tensorflow',
    'torch',
    'tkinter',
    'pygame',
    'voice',
]

block_cipher = None

a = Analysis(
    ['gui_app.py'],
    pathex=[project_root],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=excludes,
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='企业微信自动回复系统_图标修复版',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=False,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=os.path.join(project_root, '2.ico') if os.path.exists(os.path.join(project_root, '2.ico')) else None,
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=False,
    upx_exclude=[],
    name='企业微信自动回复系统_图标修复版',
)
'''
    
    with open('image_fix_pack.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✅ 图标修复版spec文件创建完成")

def main():
    """主函数"""
    print("🖼️ 图标和图片修复工具")
    print("=" * 40)
    
    # 复制图片到现有dist目录
    copy_images_to_dist()
    
    # 修复图标路径
    fix_icon_paths_in_exe()
    
    # 创建图片测试脚本
    create_image_test_script()
    
    # 创建包含图片的增强spec文件
    create_enhanced_spec_with_images()
    
    print("\n" + "=" * 40)
    print("🎉 图标和图片修复完成！")
    print("=" * 40)
    print("💡 现在您可以:")
    print("1. 测试现有版本: 运行任意dist目录中的image_test.py")
    print("2. 重新打包: pack_env\\Scripts\\python.exe -m PyInstaller image_fix_pack.spec")
    print("3. 然后运行: python fix_qt_plugins.py")
    print("\n✅ 所有图片文件应该都能正确显示了！")

if __name__ == "__main__":
    main()
