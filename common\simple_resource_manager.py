#!/usr/bin/env python3
# encoding:utf-8
"""
简化的资源管理器 - 使用外部文件，避免PyInstaller兼容性问题
"""

import os
import sys
from pathlib import Path

def get_resource_path(filename):
    """
    获取资源文件路径
    
    Args:
        filename (str): 文件名，如 "2.ico" 或 "12.jpg"
    
    Returns:
        str: 资源文件的绝对路径
    """
    # 获取exe文件所在目录（打包后）或当前目录（开发环境）
    if getattr(sys, 'frozen', False):
        # PyInstaller打包后的情况
        base_dir = os.path.dirname(sys.executable)
    else:
        # 开发环境
        base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    
    # 尝试多个可能的路径
    possible_paths = [
        os.path.join(base_dir, filename),                    # exe同目录
        os.path.join(os.getcwd(), filename),                 # 当前工作目录
        os.path.join(base_dir, 'resources', filename),       # resources子目录
        os.path.join(base_dir, 'assets', filename),          # assets子目录
    ]
    
    # 返回第一个存在的文件路径
    for path in possible_paths:
        if os.path.exists(path):
            return path
    
    # 如果都不存在，返回exe同目录的路径（让调用者处理）
    return possible_paths[0]

def get_icon_path():
    """获取程序图标路径"""
    return get_resource_path("2.ico")

def get_contact_image_path():
    """获取联系方式图片路径"""
    return get_resource_path("12.jpg")

def get_donate_image_path():
    """获取捐赠二维码路径"""
    return get_resource_path("33.png")

def debug_resources():
    """调试资源文件路径"""
    print("🔍 简化资源管理器调试信息:")
    print(f"   sys.frozen: {getattr(sys, 'frozen', False)}")
    print(f"   sys.executable: {sys.executable}")
    print(f"   当前工作目录: {os.getcwd()}")
    
    if getattr(sys, 'frozen', False):
        base_dir = os.path.dirname(sys.executable)
        print(f"   exe目录: {base_dir}")
    else:
        base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        print(f"   项目根目录: {base_dir}")
    
    print("🔍 资源文件检查:")
    for name, func in [
        ("程序图标", get_icon_path),
        ("联系方式图片", get_contact_image_path),
        ("捐赠二维码", get_donate_image_path)
    ]:
        path = func()
        exists = os.path.exists(path)
        print(f"   {name}: {'✅' if exists else '❌'} {path}")
        if exists:
            size = os.path.getsize(path)
            print(f"      文件大小: {size:,} 字节")

if __name__ == "__main__":
    debug_resources()
