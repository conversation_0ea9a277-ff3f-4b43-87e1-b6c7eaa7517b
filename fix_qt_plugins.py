#!/usr/bin/env python3
# encoding:utf-8
"""
修复Qt平台插件问题
"""

import os
import sys
import shutil
from pathlib import Path

def fix_qt_plugins():
    """修复Qt平台插件"""
    print("🔧 修复Qt平台插件...")
    
    # 查找PyQt5安装位置
    try:
        import PyQt5
        pyqt5_path = Path(PyQt5.__file__).parent
        print(f"PyQt5路径: {pyqt5_path}")
        
        # 查找Qt插件目录
        qt_plugins_path = pyqt5_path / "Qt5" / "plugins"
        if qt_plugins_path.exists():
            print(f"Qt插件路径: {qt_plugins_path}")
            
            # 检查platforms插件
            platforms_path = qt_plugins_path / "platforms"
            if platforms_path.exists():
                print("✅ 找到platforms插件目录")
                
                # 列出可用的平台插件
                platform_files = list(platforms_path.glob("*.dll"))
                print(f"可用平台插件: {[f.name for f in platform_files]}")
                
                return str(qt_plugins_path)
            else:
                print("❌ 未找到platforms插件目录")
        else:
            print("❌ 未找到Qt插件目录")
            
    except ImportError:
        print("❌ 未找到PyQt5")
    
    return None

def copy_qt_plugins_to_dist():
    """复制Qt插件到dist目录"""
    qt_plugins_path = fix_qt_plugins()
    
    if qt_plugins_path:
        # 检查可能的目录
        dist_paths = [
            Path("dist/企业微信自动回复系统_资源确保版"),
            Path("dist/企业微信自动回复系统_Hook版"),
            Path("dist/企业微信自动回复系统_简化版"),
            Path("dist/基础功能测试"),
            Path("dist/简单测试应用"),
            Path("dist/企业微信自动回复系统_调试控制台版"),
            Path("dist/企业微信自动回复系统_终极解决版"),
            Path("dist/企业微信自动回复系统_最终版"),
            Path("dist/企业微信自动回复系统_调试版"),
            Path("dist/企业微信自动回复系统_终极修复版"),
            Path("dist/企业微信自动回复系统_路径修复版"),
            Path("dist/企业微信自动回复系统_图标显示修复版"),
            Path("dist/企业微信自动回复系统_图标修复版"),
            Path("dist/企业微信自动回复系统_完整版"),
            Path("dist/企业微信自动回复系统_修复版"),
            Path("dist/企业微信自动回复系统_增强版"),
            Path("dist/企业微信自动回复系统")
        ]

        dist_path = None
        for path in dist_paths:
            if path.exists():
                dist_path = path
                break

        if dist_path:
            # 复制platforms插件
            platforms_src = Path(qt_plugins_path) / "platforms"
            platforms_dst = dist_path / "platforms"
            
            if platforms_src.exists():
                if platforms_dst.exists():
                    shutil.rmtree(platforms_dst)
                shutil.copytree(platforms_src, platforms_dst)
                print(f"✅ 复制platforms插件到: {platforms_dst}")
                
                # 创建qt.conf文件
                qt_conf_content = """[Paths]
Plugins = platforms
"""
                qt_conf_path = dist_path / "qt.conf"
                with open(qt_conf_path, 'w') as f:
                    f.write(qt_conf_content)
                print(f"✅ 创建qt.conf文件: {qt_conf_path}")
                
                return True
        else:
            print("❌ dist目录不存在")
    
    return False

def main():
    """主函数"""
    print("🔧 Qt平台插件修复工具")
    print("=" * 40)
    
    if copy_qt_plugins_to_dist():
        print("✅ Qt插件修复完成！")
        print("现在可以尝试运行exe文件")
    else:
        print("❌ Qt插件修复失败")

if __name__ == "__main__":
    main()
