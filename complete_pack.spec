# -*- mode: python ; coding: utf-8 -*-

import os
from pathlib import Path

# 项目根目录
project_root = os.path.dirname(os.path.abspath(SPEC))

# 数据文件
datas = [
    (os.path.join(project_root, 'config-template.json'), '.'),
    (os.path.join(project_root, 'config-gui.json'), '.'),
    (os.path.join(project_root, '2.ico'), '.'),
]

# 手动添加ntwork目录
ntwork_path = os.path.join(project_root, 'ntwork')
if os.path.exists(ntwork_path):
    for root, dirs, files in os.walk(ntwork_path):
        for file in files:
            if not file.endswith('.pyc') and '__pycache__' not in root:
                src_path = os.path.join(root, file)
                rel_path = os.path.relpath(src_path, project_root)
                dest_path = os.path.dirname(rel_path)
                datas.append((src_path, dest_path))

# 包含所有可能的隐藏导入
hiddenimports = [
    # PyQt5相关
    'PyQt5.QtCore',
    'PyQt5.QtGui', 
    'PyQt5.QtWidgets',
    'PyQt5.sip',
    'PyQt5.QtPrintSupport',
    
    # PIL/Pillow相关
    'PIL',
    'PIL.Image',
    'PIL.ImageTk',
    'PIL.ImageDraw',
    'PIL.ImageFont',
    'PIL.ImageFilter',
    'PIL.ImageEnhance',
    'PIL.ImageOps',
    'PIL.ImageChops',
    'PIL.ImageStat',
    'PIL.ImageColor',
    'PIL.ImageFile',
    'PIL.ImageSequence',
    'PIL.ImageShow',
    'PIL.ImageWin',
    'PIL.ImageGrab',
    'PIL.ImagePath',
    'PIL.ImageQt',
    'PIL.ImageCms',
    'PIL.ImageMath',
    'PIL.ImageMode',
    'PIL.ImagePalette',
    'PIL.ImageTransform',
    'PIL._imaging',
    'PIL._imagingft',
    'PIL._imagingmath',
    'PIL._imagingmorph',
    'PIL._imagingcms',
    'PIL._webp',
    'PIL._tkinter_finder',
    
    # ntwork核心模块
    'ntwork',
    'ntwork.core',
    'ntwork.core.wework',
    'ntwork.core.mgr',
    'ntwork.utils',
    'ntwork.utils.logger',
    'ntwork.utils.singleton',
    'ntwork.utils.xdg',
    'ntwork.const',
    'ntwork.const.notify_type',
    'ntwork.const.send_type',
    'ntwork.exception',
    'ntwork.wc',
    
    # ntwork依赖
    'pilk',
    'pyee',
    'pyee.base',
    'pyee.asyncio',
    'pyee.twisted',
    'pyee.executor',
    'pyee.trio',
    'websockets',
    'websockets.client',
    'websockets.server',
    'websockets.protocol',
    'websockets.exceptions',
    'websockets.extensions',
    'websockets.headers',
    'websockets.http',
    'websockets.legacy',
    'websockets.typing',
    'websockets.uri',
    'aiohttp',
    'aiohttp.client',
    'aiohttp.web',
    'aiohttp.connector',
    'aiohttp.helpers',
    'aiohttp.http',
    'aiohttp.multipart',
    'aiohttp.payload',
    'aiohttp.resolver',
    'aiohttp.streams',
    'aiohttp.tracing',
    'aiohttp.typedefs',
    'aiohttp.web_exceptions',
    'aiohttp.web_middlewares',
    'aiohttp.web_protocol',
    'aiohttp.web_request',
    'aiohttp.web_response',
    'aiohttp.web_runner',
    'aiohttp.web_urldispatcher',
    'aiohttp.web_ws',
    
    # 应用程序模块
    'gui.main_window',
    'gui.controllers',
    'gui.config_loader',
    'gui.managers',
    'gui.system_tray',
    'config',
    'common.log',
    'common.utils',
    'common.singleton',
    'bridge.bridge',
    'bridge.context',
    'bridge.reply',
    'bot.bot_factory',
    'channel.channel_factory',
    'plugins.plugin_manager',
    
    # 标准库和其他依赖
    'requests',
    'requests.adapters',
    'requests.auth',
    'requests.cookies',
    'requests.exceptions',
    'requests.hooks',
    'requests.models',
    'requests.packages',
    'requests.sessions',
    'requests.structures',
    'requests.utils',
    'json',
    'threading',
    'queue',
    'sqlite3',
    'configparser',
    'logging',
    'ctypes',
    'ctypes.wintypes',
    'win32api',
    'win32con',
    'win32gui',
    'pywintypes',
    'asyncio',
    'typing_extensions',
    'dataclasses',
    'datetime',
    'pathlib',
    'os',
    'sys',
    'time',
    'uuid',
    'hashlib',
    'base64',
    'urllib',
    'urllib.parse',
    'urllib.request',
    'http',
    'http.client',
    'ssl',
    'socket',
    'select',
    'errno',
    'struct',
    'binascii',
    'zlib',
    'gzip',
    'io',
    'collections',
    'functools',
    'itertools',
    'operator',
    'weakref',
    'copy',
    'pickle',
    'tempfile',
    'shutil',
    'glob',
    'fnmatch',
    'linecache',
    'tokenize',
    'keyword',
    'heapq',
    'bisect',
    'array',
    'types',
    'inspect',
    'dis',
    'opcode',
    'ast',
    'symtable',
    'symbol',
    'token',
    'parser',
    'compileall',
    'py_compile',
    'zipimport',
    'pkgutil',
    'modulefinder',
    'runpy',
    'importlib',
    'importlib.util',
    'importlib.machinery',
    'site',
    'sysconfig',
    'platform',
    'warnings',
    'contextlib',
    'abc',
    'atexit',
    'traceback',
    'linecache',
    'reprlib',
    'enum',
    'dataclasses',
]

# 排除模块
excludes = [
    'matplotlib',
    'scipy',
    'tensorflow',
    'torch',
    'tkinter',
    'pygame',
    'voice',
]

block_cipher = None

a = Analysis(
    ['gui_app.py'],
    pathex=[project_root],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=excludes,
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='企业微信自动回复系统_完整版',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=False,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=os.path.join(project_root, '2.ico') if os.path.exists(os.path.join(project_root, '2.ico')) else None,
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=False,
    upx_exclude=[],
    name='企业微信自动回复系统_完整版',
)
