#!/usr/bin/env python3
# encoding:utf-8
"""
终极图标和图片显示修复方案
直接修改源代码，确保在PyInstaller环境下正确工作
"""

import os
import sys
import shutil
from pathlib import Path

def create_resource_manager():
    """创建资源管理器模块"""
    print("📝 创建资源管理器模块...")
    
    resource_manager_code = '''#!/usr/bin/env python3
# encoding:utf-8
"""
资源管理器 - 专门处理PyInstaller打包后的资源文件路径问题
"""

import os
import sys
from pathlib import Path

class ResourceManager:
    """资源管理器类"""
    
    @staticmethod
    def get_base_path():
        """获取基础路径"""
        if getattr(sys, 'frozen', False):
            # PyInstaller打包后的情况
            if hasattr(sys, '_MEIPASS'):
                # 临时解压目录
                return sys._MEIPASS
            else:
                # exe文件所在目录
                return os.path.dirname(sys.executable)
        else:
            # 开发环境
            return os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    
    @staticmethod
    def get_resource_path(filename):
        """获取资源文件路径"""
        base_path = ResourceManager.get_base_path()
        
        # 尝试多个可能的路径
        possible_paths = [
            os.path.join(base_path, filename),  # 基础路径
            os.path.join(os.path.dirname(sys.executable), filename),  # exe目录
            os.path.join(os.getcwd(), filename),  # 当前工作目录
        ]
        
        for path in possible_paths:
            if os.path.exists(path):
                return path
        
        # 如果都找不到，返回基础路径下的文件名
        return os.path.join(base_path, filename)
    
    @staticmethod
    def get_icon_path():
        """获取程序图标路径"""
        return ResourceManager.get_resource_path("2.ico")
    
    @staticmethod
    def get_contact_image_path():
        """获取联系方式图片路径"""
        return ResourceManager.get_resource_path("12.jpg")
    
    @staticmethod
    def get_donate_image_path():
        """获取捐赠二维码路径"""
        return ResourceManager.get_resource_path("33.png")
    
    @staticmethod
    def debug_info():
        """输出调试信息"""
        print("🔍 资源管理器调试信息:")
        print(f"   sys.frozen: {getattr(sys, 'frozen', False)}")
        print(f"   sys._MEIPASS: {getattr(sys, '_MEIPASS', '未定义')}")
        print(f"   sys.executable: {sys.executable}")
        print(f"   os.getcwd(): {os.getcwd()}")
        print(f"   基础路径: {ResourceManager.get_base_path()}")
        
        files = [
            ("程序图标", ResourceManager.get_icon_path()),
            ("联系方式图片", ResourceManager.get_contact_image_path()),
            ("捐赠二维码", ResourceManager.get_donate_image_path())
        ]
        
        for name, path in files:
            exists = os.path.exists(path)
            print(f"   {name}: {'✅' if exists else '❌'} {path}")

# 全局实例
resource_manager = ResourceManager()

# 便捷函数
def get_icon_path():
    return resource_manager.get_icon_path()

def get_contact_image_path():
    return resource_manager.get_contact_image_path()

def get_donate_image_path():
    return resource_manager.get_donate_image_path()

if __name__ == "__main__":
    ResourceManager.debug_info()
'''
    
    # 保存到common目录
    common_dir = Path("common")
    if not common_dir.exists():
        common_dir.mkdir()
    
    resource_manager_path = common_dir / "resource_manager.py"
    with open(resource_manager_path, 'w', encoding='utf-8') as f:
        f.write(resource_manager_code)
    
    print(f"✅ 资源管理器创建完成: {resource_manager_path}")

def fix_main_window():
    """修复主窗口的图标设置"""
    print("🔧 修复主窗口图标设置...")
    
    main_window_file = Path("gui/main_window.py")
    if not main_window_file.exists():
        print("❌ gui/main_window.py 不存在")
        return False
    
    # 读取文件
    with open(main_window_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 添加导入
    if "from common.resource_manager import get_icon_path" not in content:
        # 在PyQt5导入后添加
        import_pos = content.find("from PyQt5.QtCore import")
        if import_pos != -1:
            line_end = content.find('\n', import_pos)
            new_import = "\nfrom common.resource_manager import get_icon_path"
            content = content[:line_end] + new_import + content[line_end:]
    
    # 完全替换图标设置函数
    old_function_start = "    def set_window_icon(self):"
    old_function_end = "            print(f\"设置窗口图标失败: {e}\")"
    
    start_pos = content.find(old_function_start)
    if start_pos != -1:
        end_pos = content.find(old_function_end, start_pos)
        if end_pos != -1:
            end_pos = content.find('\n', end_pos) + 1
            
            new_function = '''    def set_window_icon(self):
        """设置窗口图标 - 修复版"""
        try:
            icon_path = get_icon_path()
            print(f"尝试加载图标: {icon_path}")
            
            if os.path.exists(icon_path):
                icon = QIcon(icon_path)
                if not icon.isNull():
                    self.setWindowIcon(icon)
                    print(f"✅ 窗口图标设置成功: {icon_path}")
                    return
                else:
                    print(f"❌ 图标文件无效: {icon_path}")
            else:
                print(f"❌ 图标文件不存在: {icon_path}")
            
            # 创建默认图标
            self.create_default_icon()
            
        except Exception as e:
            print(f"❌ 设置窗口图标失败: {e}")
            self.create_default_icon()
    
    def create_default_icon(self):
        """创建默认图标"""
        try:
            from PyQt5.QtGui import QPixmap, QPainter, QBrush, QColor
            
            pixmap = QPixmap(32, 32)
            pixmap.fill()
            
            painter = QPainter(pixmap)
            painter.setRenderHint(QPainter.Antialiasing)
            painter.setBrush(QBrush(QColor(0, 120, 215)))
            painter.drawEllipse(4, 4, 24, 24)
            painter.setPen(QColor(255, 255, 255))
            painter.drawText(12, 20, "W")
            painter.end()
            
            icon = QIcon(pixmap)
            self.setWindowIcon(icon)
            print("✅ 使用默认图标")
            
        except Exception as e:
            print(f"❌ 创建默认图标失败: {e}")
'''
            
            content = content[:start_pos] + new_function + content[end_pos:]
    
    # 保存文件
    with open(main_window_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ 主窗口图标设置已修复")
    return True

def fix_help_window():
    """修复帮助窗口的图片加载"""
    print("🔧 修复帮助窗口图片加载...")
    
    help_window_file = Path("gui/help_window.py")
    if not help_window_file.exists():
        print("❌ gui/help_window.py 不存在")
        return False
    
    # 读取文件
    with open(help_window_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 添加导入
    if "from common.resource_manager import get_contact_image_path" not in content:
        import_pos = content.find("from PyQt5.QtCore import")
        if import_pos != -1:
            line_end = content.find('\n', import_pos)
            new_import = "\nfrom common.resource_manager import get_contact_image_path"
            content = content[:line_end] + new_import + content[line_end:]
    
    # 替换图片加载代码
    old_code = '''        # 尝试加载图片
        try:
            import os
            image_path = get_contact_image_path()
            if os.path.exists(image_path):'''
    
    new_code = '''        # 尝试加载图片 - 修复版
        try:
            import os
            image_path = get_contact_image_path()
            print(f"尝试加载联系方式图片: {image_path}")
            
            if os.path.exists(image_path):'''
    
    content = content.replace(old_code, new_code)
    
    # 保存文件
    with open(help_window_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ 帮助窗口图片加载已修复")
    return True

def fix_donate_window():
    """修复捐赠窗口的图片加载"""
    print("🔧 修复捐赠窗口图片加载...")
    
    donate_window_file = Path("gui/donate_window.py")
    if not donate_window_file.exists():
        print("❌ gui/donate_window.py 不存在")
        return False
    
    # 读取文件
    with open(donate_window_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 添加导入
    if "from common.resource_manager import get_donate_image_path" not in content:
        import_pos = content.find("from PyQt5.QtCore import")
        if import_pos != -1:
            line_end = content.find('\n', import_pos)
            new_import = "\nfrom common.resource_manager import get_donate_image_path"
            content = content[:line_end] + new_import + content[line_end:]
    
    # 替换图片加载代码
    old_code = '''            image_path = get_donate_image_path()'''
    new_code = '''            image_path = get_donate_image_path()
            print(f"尝试加载捐赠二维码: {image_path}")'''
    
    content = content.replace(old_code, new_code)
    
    # 保存文件
    with open(donate_window_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ 捐赠窗口图片加载已修复")
    return True

def fix_system_tray():
    """修复系统托盘图标"""
    print("🔧 修复系统托盘图标...")
    
    system_tray_file = Path("gui/system_tray.py")
    if not system_tray_file.exists():
        print("❌ gui/system_tray.py 不存在")
        return False
    
    # 读取文件
    with open(system_tray_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 添加导入
    if "from common.resource_manager import get_icon_path" not in content:
        import_pos = content.find("from PyQt5.QtCore import")
        if import_pos != -1:
            line_end = content.find('\n', import_pos)
            new_import = "\nfrom common.resource_manager import get_icon_path"
            content = content[:line_end] + new_import + content[line_end:]
    
    # 替换图标加载代码
    old_code = '''            # 尝试使用2.ico文件
            icon_path = get_icon_path()'''
    new_code = '''            # 尝试使用2.ico文件
            icon_path = get_icon_path()
            print(f"尝试加载系统托盘图标: {icon_path}")'''
    
    content = content.replace(old_code, new_code)
    
    # 保存文件
    with open(system_tray_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ 系统托盘图标已修复")
    return True

def create_ultimate_spec():
    """创建终极修复版spec文件"""
    print("📝 创建终极修复版spec文件...")
    
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

import os
from pathlib import Path

# 项目根目录
project_root = os.path.dirname(os.path.abspath(SPEC))

# 数据文件 - 确保所有资源文件都在正确位置
datas = []

# 配置文件
config_files = ['config-template.json', 'config-gui.json']
for config_file in config_files:
    config_path = os.path.join(project_root, config_file)
    if os.path.exists(config_path):
        datas.append((config_path, '.'))
        print(f"添加配置文件: {config_file}")

# 图片文件 - 放在根目录，确保资源管理器能找到
image_files = ['2.ico', '12.jpg', '33.png']
for image_file in image_files:
    image_path = os.path.join(project_root, image_file)
    if os.path.exists(image_path):
        datas.append((image_path, '.'))
        print(f"添加图片文件到根目录: {image_file}")
    else:
        print(f"警告: 图片文件不存在: {image_path}")

# ntwork目录
ntwork_path = os.path.join(project_root, 'ntwork')
if os.path.exists(ntwork_path):
    for root, dirs, files in os.walk(ntwork_path):
        for file in files:
            if not file.endswith('.pyc') and '__pycache__' not in root:
                src_path = os.path.join(root, file)
                rel_path = os.path.relpath(src_path, project_root)
                dest_path = os.path.dirname(rel_path)
                datas.append((src_path, dest_path))

# 隐藏导入
hiddenimports = [
    # PyQt5
    'PyQt5.QtCore', 'PyQt5.QtGui', 'PyQt5.QtWidgets', 'PyQt5.sip', 'PyQt5.QtPrintSupport',
    
    # PIL/Pillow
    'PIL', 'PIL.Image', 'PIL.ImageTk', 'PIL.ImageDraw', 'PIL.ImageFont', 'PIL.ImageFilter',
    'PIL.ImageEnhance', 'PIL.ImageOps', 'PIL.ImageChops', 'PIL.ImageStat', 'PIL.ImageColor',
    'PIL.ImageFile', 'PIL.ImageSequence', 'PIL.ImageShow', 'PIL.ImageWin', 'PIL.ImageGrab',
    'PIL.ImagePath', 'PIL.ImageQt', 'PIL.ImageCms', 'PIL.ImageMath', 'PIL.ImageMode',
    'PIL.ImagePalette', 'PIL.ImageTransform', 'PIL._imaging', 'PIL._imagingft',
    'PIL._imagingmath', 'PIL._imagingmorph', 'PIL._imagingcms', 'PIL._webp', 'PIL._tkinter_finder',
    
    # ntwork
    'ntwork', 'ntwork.core', 'ntwork.core.wework', 'ntwork.core.mgr', 'ntwork.utils',
    'ntwork.utils.logger', 'ntwork.utils.singleton', 'ntwork.utils.xdg', 'ntwork.const',
    'ntwork.const.notify_type', 'ntwork.const.send_type', 'ntwork.exception', 'ntwork.wc',
    
    # 依赖
    'pilk', 'pyee', 'pyee.base', 'pyee.asyncio', 'websockets', 'websockets.client',
    'websockets.server', 'aiohttp', 'aiohttp.client', 'aiohttp.web',
    
    # 应用模块
    'gui.main_window', 'gui.controllers', 'gui.config_loader', 'gui.managers',
    'gui.system_tray', 'gui.help_window', 'gui.donate_window', 'config',
    'common.log', 'common.utils', 'common.singleton', 'common.resource_manager',
    'bridge.bridge', 'bridge.context', 'bridge.reply', 'bot.bot_factory',
    'channel.channel_factory', 'plugins.plugin_manager',
    
    # 其他
    'requests', 'json', 'threading', 'queue', 'sqlite3', 'configparser', 'logging',
    'ctypes', 'ctypes.wintypes', 'win32api', 'win32con', 'win32gui', 'pywintypes',
    'asyncio', 'typing_extensions', 'dataclasses',
]

excludes = ['matplotlib', 'scipy', 'tensorflow', 'torch', 'tkinter', 'pygame', 'voice']

block_cipher = None

a = Analysis(
    ['gui_app.py'],
    pathex=[project_root],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=excludes,
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

icon_path = os.path.join(project_root, '2.ico')
if not os.path.exists(icon_path):
    print(f"警告: 图标文件不存在: {icon_path}")
    icon_path = None

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='企业微信自动回复系统_终极修复版',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=False,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=icon_path,
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=False,
    upx_exclude=[],
    name='企业微信自动回复系统_终极修复版',
)
'''
    
    with open('ultimate_fix.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✅ 终极修复版spec文件创建完成")

def main():
    """主函数"""
    print("🚀 终极图标和图片显示修复工具")
    print("=" * 60)
    
    # 创建资源管理器
    create_resource_manager()
    
    # 修复所有模块
    fix_main_window()
    fix_help_window()
    fix_donate_window()
    fix_system_tray()
    
    # 创建终极spec文件
    create_ultimate_spec()
    
    print("\n" + "=" * 60)
    print("🎉 终极修复完成！")
    print("=" * 60)
    print("💡 执行步骤:")
    print("1. 运行: pack_env\\Scripts\\python.exe -m PyInstaller ultimate_fix.spec")
    print("2. 运行: python fix_qt_plugins.py")
    print("3. 测试: 运行生成的exe文件")
    print("\n✅ 这次应该能彻底解决所有图标和图片显示问题！")

if __name__ == "__main__":
    main()
