#!/usr/bin/env python3
# encoding:utf-8
"""
创建简化的外部文件解决方案
"""

import os
import sys
import shutil
from pathlib import Path

def create_simple_resource_manager():
    """创建简化的资源管理器"""
    print("📝 创建简化的资源管理器...")
    
    simple_resource_code = '''#!/usr/bin/env python3
# encoding:utf-8
"""
简化的资源管理器 - 使用外部文件，避免PyInstaller兼容性问题
"""

import os
import sys
from pathlib import Path

def get_resource_path(filename):
    """
    获取资源文件路径
    
    Args:
        filename (str): 文件名，如 "2.ico" 或 "12.jpg"
    
    Returns:
        str: 资源文件的绝对路径
    """
    # 获取exe文件所在目录（打包后）或当前目录（开发环境）
    if getattr(sys, 'frozen', False):
        # PyInstaller打包后的情况
        base_dir = os.path.dirname(sys.executable)
    else:
        # 开发环境
        base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    
    # 尝试多个可能的路径
    possible_paths = [
        os.path.join(base_dir, filename),                    # exe同目录
        os.path.join(os.getcwd(), filename),                 # 当前工作目录
        os.path.join(base_dir, 'resources', filename),       # resources子目录
        os.path.join(base_dir, 'assets', filename),          # assets子目录
    ]
    
    # 返回第一个存在的文件路径
    for path in possible_paths:
        if os.path.exists(path):
            return path
    
    # 如果都不存在，返回exe同目录的路径（让调用者处理）
    return possible_paths[0]

def get_icon_path():
    """获取程序图标路径"""
    return get_resource_path("2.ico")

def get_contact_image_path():
    """获取联系方式图片路径"""
    return get_resource_path("12.jpg")

def get_donate_image_path():
    """获取捐赠二维码路径"""
    return get_resource_path("33.png")

def debug_resources():
    """调试资源文件路径"""
    print("🔍 简化资源管理器调试信息:")
    print(f"   sys.frozen: {getattr(sys, 'frozen', False)}")
    print(f"   sys.executable: {sys.executable}")
    print(f"   当前工作目录: {os.getcwd()}")
    
    if getattr(sys, 'frozen', False):
        base_dir = os.path.dirname(sys.executable)
        print(f"   exe目录: {base_dir}")
    else:
        base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        print(f"   项目根目录: {base_dir}")
    
    print("🔍 资源文件检查:")
    for name, func in [
        ("程序图标", get_icon_path),
        ("联系方式图片", get_contact_image_path),
        ("捐赠二维码", get_donate_image_path)
    ]:
        path = func()
        exists = os.path.exists(path)
        print(f"   {name}: {'✅' if exists else '❌'} {path}")
        if exists:
            size = os.path.getsize(path)
            print(f"      文件大小: {size:,} 字节")

if __name__ == "__main__":
    debug_resources()
'''
    
    # 保存简化资源管理器
    common_dir = Path("common")
    if not common_dir.exists():
        common_dir.mkdir()
    
    simple_resource_path = common_dir / "simple_resource_manager.py"
    with open(simple_resource_path, 'w', encoding='utf-8') as f:
        f.write(simple_resource_code)
    
    print(f"✅ 简化资源管理器创建完成: {simple_resource_path}")

def update_all_modules():
    """更新所有模块使用简化资源管理器"""
    print("🔧 更新所有模块使用简化资源管理器...")
    
    modules_to_update = [
        ("gui/main_window.py", "get_icon_path"),
        ("gui/help_window.py", "get_contact_image_path"),
        ("gui/donate_window.py", "get_donate_image_path"),
        ("gui/system_tray.py", "get_icon_path"),
    ]
    
    for module_path, function_name in modules_to_update:
        module_file = Path(module_path)
        if not module_file.exists():
            print(f"⚠️ 模块不存在: {module_path}")
            continue
        
        # 读取文件
        with open(module_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 替换导入语句
        old_imports = [
            "from common.embedded_resources import",
            "from common.resource_manager import",
        ]
        
        new_import = "from common.simple_resource_manager import"
        
        # 替换所有可能的旧导入
        for old_import in old_imports:
            if old_import in content:
                # 找到完整的导入行
                lines = content.split('\n')
                for i, line in enumerate(lines):
                    if old_import in line:
                        # 提取导入的函数名
                        if function_name in line:
                            lines[i] = f"{new_import} {function_name}"
                            break
                content = '\n'.join(lines)
                break
        
        # 如果没有找到导入，添加新导入
        if new_import not in content:
            # 在PyQt5导入后添加
            import_pos = content.find("from PyQt5.QtCore import")
            if import_pos != -1:
                line_end = content.find('\n', import_pos)
                content = content[:line_end] + f"\n{new_import} {function_name}" + content[line_end:]
        
        # 保存文件
        with open(module_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"✅ 已更新: {module_path}")

def create_simple_spec():
    """创建简化方案的spec文件"""
    print("📝 创建简化方案的spec文件...")
    
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-
# 简化方案 - 使用外部文件避免嵌入式资源问题

import os
from pathlib import Path

project_root = os.path.dirname(os.path.abspath(SPEC))

# 数据文件 - 包含图片文件和配置文件
datas = []

# 图片文件 - 放在根目录
image_files = ['2.ico', '12.jpg', '33.png']
for image_file in image_files:
    image_path = os.path.join(project_root, image_file)
    if os.path.exists(image_path):
        datas.append((image_path, '.'))
        print(f"添加图片文件: {image_file}")

# 配置文件
config_files = ['config-template.json', 'config-gui.json']
for config_file in config_files:
    config_path = os.path.join(project_root, config_file)
    if os.path.exists(config_path):
        datas.append((config_path, '.'))

# ntwork目录
ntwork_path = os.path.join(project_root, 'ntwork')
if os.path.exists(ntwork_path):
    for root, dirs, files in os.walk(ntwork_path):
        for file in files:
            if not file.endswith('.pyc') and '__pycache__' not in root:
                src_path = os.path.join(root, file)
                rel_path = os.path.relpath(src_path, project_root)
                dest_path = os.path.dirname(rel_path)
                datas.append((src_path, dest_path))

# 隐藏导入
hiddenimports = [
    # PyQt5
    'PyQt5.QtCore', 'PyQt5.QtGui', 'PyQt5.QtWidgets', 'PyQt5.sip', 'PyQt5.QtPrintSupport',
    
    # PIL/Pillow
    'PIL', 'PIL.Image', 'PIL.ImageTk', 'PIL.ImageDraw', 'PIL.ImageFont', 'PIL.ImageFilter',
    'PIL.ImageEnhance', 'PIL.ImageOps', 'PIL.ImageChops', 'PIL.ImageStat', 'PIL.ImageColor',
    'PIL.ImageFile', 'PIL.ImageSequence', 'PIL.ImageShow', 'PIL.ImageWin', 'PIL.ImageGrab',
    'PIL.ImagePath', 'PIL.ImageQt', 'PIL.ImageCms', 'PIL.ImageMath', 'PIL.ImageMode',
    'PIL.ImagePalette', 'PIL.ImageTransform', 'PIL._imaging', 'PIL._imagingft',
    'PIL._imagingmath', 'PIL._imagingmorph', 'PIL._imagingcms', 'PIL._webp', 'PIL._tkinter_finder',
    
    # ntwork
    'ntwork', 'ntwork.core', 'ntwork.core.wework', 'ntwork.core.mgr', 'ntwork.utils',
    'ntwork.utils.logger', 'ntwork.utils.singleton', 'ntwork.utils.xdg', 'ntwork.const',
    'ntwork.const.notify_type', 'ntwork.const.send_type', 'ntwork.exception', 'ntwork.wc',
    
    # 依赖
    'pilk', 'pyee', 'pyee.base', 'pyee.asyncio', 'websockets', 'websockets.client',
    'websockets.server', 'aiohttp', 'aiohttp.client', 'aiohttp.web',
    
    # 应用模块
    'gui.main_window', 'gui.controllers', 'gui.config_loader', 'gui.managers',
    'gui.system_tray', 'gui.help_window', 'gui.donate_window', 'config',
    'common.log', 'common.utils', 'common.singleton', 'common.simple_resource_manager',
    'bridge.bridge', 'bridge.context', 'bridge.reply', 'bot.bot_factory',
    'channel.channel_factory', 'plugins.plugin_manager',
    
    # 其他
    'requests', 'json', 'threading', 'queue', 'sqlite3', 'configparser', 'logging',
    'ctypes', 'ctypes.wintypes', 'win32api', 'win32con', 'win32gui', 'pywintypes',
    'asyncio', 'typing_extensions', 'dataclasses',
]

excludes = ['matplotlib', 'scipy', 'tensorflow', 'torch', 'tkinter', 'pygame', 'voice']

block_cipher = None

a = Analysis(
    ['gui_app.py'],
    pathex=[project_root],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=excludes,
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

icon_path = os.path.join(project_root, '2.ico')

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='企业微信自动回复系统_简化版',
    debug=False,  # 关闭调试
    bootloader_ignore_signals=False,
    strip=False,
    upx=False,
    console=False,  # 无控制台版本
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=icon_path if os.path.exists(icon_path) else None,
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=False,
    upx_exclude=[],
    name='企业微信自动回复系统_简化版',
)
'''
    
    with open('simple_solution.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✅ 简化方案spec文件创建完成")

def main():
    """主函数"""
    print("🚀 创建简化的外部文件解决方案")
    print("=" * 60)
    
    # 创建简化资源管理器
    create_simple_resource_manager()
    
    # 更新所有模块
    update_all_modules()
    
    # 创建简化spec文件
    create_simple_spec()
    
    print("\n" + "=" * 60)
    print("🎉 简化解决方案创建完成！")
    print("=" * 60)
    print("💡 执行步骤:")
    print("1. 运行: pack_env\\Scripts\\python.exe -m PyInstaller simple_solution.spec")
    print("2. 运行: python fix_qt_plugins.py")
    print("3. 测试: 运行生成的exe文件")
    print("")
    print("✅ 优势:")
    print("   - 避免了嵌入式资源的PyInstaller兼容性问题")
    print("   - 使用简单可靠的外部文件方案")
    print("   - 图片文件直接放在exe同目录，易于管理")
    print("   - 无需复杂的base64编码和临时文件")
    print("")
    print("🎯 这次应该能彻底解决图标和图片显示问题！")

if __name__ == "__main__":
    main()
