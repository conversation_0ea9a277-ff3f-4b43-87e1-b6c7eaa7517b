#!/usr/bin/env python3
# encoding:utf-8
"""
彻底解决图标和图片显示问题的终极方案
基于深度分析的根本性修复
"""

import os
import sys
import shutil
import base64
from pathlib import Path

def create_embedded_resources():
    """创建嵌入式资源模块，将图片转换为base64编码"""
    print("📦 创建嵌入式资源模块...")
    
    # 读取图片文件并转换为base64
    resources = {}
    
    image_files = {
        "2.ico": "程序图标",
        "12.jpg": "联系方式图片", 
        "33.png": "捐赠二维码"
    }
    
    for filename, description in image_files.items():
        file_path = Path(filename)
        if file_path.exists():
            with open(file_path, 'rb') as f:
                data = f.read()
            encoded = base64.b64encode(data).decode('utf-8')
            resources[filename] = encoded
            print(f"✅ {description} ({filename}): {len(data)} 字节 -> {len(encoded)} 字符")
        else:
            print(f"❌ {description} ({filename}): 文件不存在")
    
    # 创建嵌入式资源模块
    resource_code = f'''#!/usr/bin/env python3
# encoding:utf-8
"""
嵌入式资源模块 - 图片数据直接嵌入代码中
这样可以完全避免PyInstaller的路径问题
"""

import base64
import os
import tempfile
from pathlib import Path

# 嵌入的图片数据（base64编码）
EMBEDDED_RESOURCES = {{
    "2.ico": """{resources.get("2.ico", "")}"",
    "12.jpg": """{resources.get("12.jpg", "")}"",
    "33.png": """{resources.get("33.png", "")}"",
}}

class EmbeddedResourceManager:
    """嵌入式资源管理器"""
    
    _temp_files = {{}}  # 缓存临时文件路径
    
    @classmethod
    def get_resource_path(cls, filename):
        """获取资源文件路径，如果不存在则从嵌入数据创建临时文件"""
        if filename not in EMBEDDED_RESOURCES:
            print(f"❌ 资源不存在: {{filename}}")
            return None
        
        # 如果已经创建过临时文件，直接返回
        if filename in cls._temp_files:
            temp_path = cls._temp_files[filename]
            if os.path.exists(temp_path):
                return temp_path
        
        try:
            # 解码base64数据
            data = base64.b64decode(EMBEDDED_RESOURCES[filename])
            
            # 创建临时文件
            suffix = Path(filename).suffix
            temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=suffix)
            temp_file.write(data)
            temp_file.close()
            
            # 缓存路径
            cls._temp_files[filename] = temp_file.name
            
            print(f"✅ 创建临时资源文件: {{filename}} -> {{temp_file.name}}")
            return temp_file.name
            
        except Exception as e:
            print(f"❌ 创建资源文件失败 {{filename}}: {{e}}")
            return None
    
    @classmethod
    def get_icon_path(cls):
        """获取程序图标路径"""
        return cls.get_resource_path("2.ico")
    
    @classmethod
    def get_contact_image_path(cls):
        """获取联系方式图片路径"""
        return cls.get_resource_path("12.jpg")
    
    @classmethod
    def get_donate_image_path(cls):
        """获取捐赠二维码路径"""
        return cls.get_resource_path("33.png")
    
    @classmethod
    def cleanup(cls):
        """清理临时文件"""
        for filename, temp_path in cls._temp_files.items():
            try:
                if os.path.exists(temp_path):
                    os.unlink(temp_path)
                    print(f"🗑️ 清理临时文件: {{filename}}")
            except Exception as e:
                print(f"⚠️ 清理临时文件失败 {{filename}}: {{e}}")
        cls._temp_files.clear()

# 便捷函数
def get_icon_path():
    return EmbeddedResourceManager.get_icon_path()

def get_contact_image_path():
    return EmbeddedResourceManager.get_contact_image_path()

def get_donate_image_path():
    return EmbeddedResourceManager.get_donate_image_path()

# 程序退出时清理临时文件
import atexit
atexit.register(EmbeddedResourceManager.cleanup)

if __name__ == "__main__":
    print("🧪 测试嵌入式资源管理器")
    print("=" * 40)
    
    for name, func in [
        ("程序图标", get_icon_path),
        ("联系方式图片", get_contact_image_path), 
        ("捐赠二维码", get_donate_image_path)
    ]:
        path = func()
        if path and os.path.exists(path):
            size = os.path.getsize(path)
            print(f"✅ {{name}}: {{path}} ({{size}} 字节)")
        else:
            print(f"❌ {{name}}: 获取失败")
'''
    
    # 保存嵌入式资源模块
    common_dir = Path("common")
    if not common_dir.exists():
        common_dir.mkdir()
    
    embedded_resources_path = common_dir / "embedded_resources.py"
    with open(embedded_resources_path, 'w', encoding='utf-8') as f:
        f.write(resource_code)
    
    print(f"✅ 嵌入式资源模块创建完成: {embedded_resources_path}")

def fix_all_modules():
    """修复所有模块使用嵌入式资源"""
    print("🔧 修复所有模块使用嵌入式资源...")
    
    # 修复主窗口
    fix_main_window()
    
    # 修复帮助窗口
    fix_help_window()
    
    # 修复捐赠窗口
    fix_donate_window()
    
    # 修复系统托盘
    fix_system_tray()

def fix_main_window():
    """修复主窗口使用嵌入式资源"""
    print("🔧 修复主窗口...")
    
    main_window_file = Path("gui/main_window.py")
    if not main_window_file.exists():
        print("❌ gui/main_window.py 不存在")
        return
    
    with open(main_window_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 替换导入
    old_import = "from common.resource_manager import get_icon_path"
    new_import = "from common.embedded_resources import get_icon_path"
    content = content.replace(old_import, new_import)
    
    # 如果没有找到旧导入，添加新导入
    if new_import not in content:
        # 在PyQt5导入后添加
        import_pos = content.find("from PyQt5.QtCore import")
        if import_pos != -1:
            line_end = content.find('\n', import_pos)
            content = content[:line_end] + f"\n{new_import}" + content[line_end:]
    
    with open(main_window_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ 主窗口已修复")

def fix_help_window():
    """修复帮助窗口使用嵌入式资源"""
    print("🔧 修复帮助窗口...")
    
    help_window_file = Path("gui/help_window.py")
    if not help_window_file.exists():
        print("❌ gui/help_window.py 不存在")
        return
    
    with open(help_window_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 替换导入
    old_import = "from common.resource_manager import get_contact_image_path"
    new_import = "from common.embedded_resources import get_contact_image_path"
    content = content.replace(old_import, new_import)
    
    # 如果没有找到旧导入，添加新导入
    if new_import not in content:
        import_pos = content.find("from PyQt5.QtCore import")
        if import_pos != -1:
            line_end = content.find('\n', import_pos)
            content = content[:line_end] + f"\n{new_import}" + content[line_end:]
    
    with open(help_window_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ 帮助窗口已修复")

def fix_donate_window():
    """修复捐赠窗口使用嵌入式资源"""
    print("🔧 修复捐赠窗口...")
    
    donate_window_file = Path("gui/donate_window.py")
    if not donate_window_file.exists():
        print("❌ gui/donate_window.py 不存在")
        return
    
    with open(donate_window_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 替换导入
    old_import = "from common.resource_manager import get_donate_image_path"
    new_import = "from common.embedded_resources import get_donate_image_path"
    content = content.replace(old_import, new_import)
    
    # 如果没有找到旧导入，添加新导入
    if new_import not in content:
        import_pos = content.find("from PyQt5.QtCore import")
        if import_pos != -1:
            line_end = content.find('\n', import_pos)
            content = content[:line_end] + f"\n{new_import}" + content[line_end:]
    
    with open(donate_window_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ 捐赠窗口已修复")

def fix_system_tray():
    """修复系统托盘使用嵌入式资源"""
    print("🔧 修复系统托盘...")
    
    system_tray_file = Path("gui/system_tray.py")
    if not system_tray_file.exists():
        print("❌ gui/system_tray.py 不存在")
        return
    
    with open(system_tray_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 替换导入
    old_import = "from common.resource_manager import get_icon_path"
    new_import = "from common.embedded_resources import get_icon_path"
    content = content.replace(old_import, new_import)
    
    # 如果没有找到旧导入，添加新导入
    if new_import not in content:
        import_pos = content.find("from PyQt5.QtCore import")
        if import_pos != -1:
            line_end = content.find('\n', import_pos)
            content = content[:line_end] + f"\n{new_import}" + content[line_end:]
    
    with open(system_tray_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ 系统托盘已修复")

def create_ultimate_spec():
    """创建终极解决方案的spec文件"""
    print("📝 创建终极解决方案spec文件...")
    
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-
# 终极解决方案 - 使用嵌入式资源完全避免路径问题

import os
from pathlib import Path

project_root = os.path.dirname(os.path.abspath(SPEC))

# 数据文件 - 只包含必要的配置文件，图片已嵌入代码
datas = []

# 配置文件
config_files = ['config-template.json', 'config-gui.json']
for config_file in config_files:
    config_path = os.path.join(project_root, config_file)
    if os.path.exists(config_path):
        datas.append((config_path, '.'))

# ntwork目录
ntwork_path = os.path.join(project_root, 'ntwork')
if os.path.exists(ntwork_path):
    for root, dirs, files in os.walk(ntwork_path):
        for file in files:
            if not file.endswith('.pyc') and '__pycache__' not in root:
                src_path = os.path.join(root, file)
                rel_path = os.path.relpath(src_path, project_root)
                dest_path = os.path.dirname(rel_path)
                datas.append((src_path, dest_path))

# 隐藏导入
hiddenimports = [
    'PyQt5.QtCore', 'PyQt5.QtGui', 'PyQt5.QtWidgets', 'PyQt5.sip', 'PyQt5.QtPrintSupport',
    'PIL', 'PIL.Image', 'PIL.ImageTk', 'PIL.ImageDraw', 'PIL.ImageFont', 'PIL.ImageFilter',
    'PIL.ImageEnhance', 'PIL.ImageOps', 'PIL.ImageChops', 'PIL.ImageStat', 'PIL.ImageColor',
    'PIL.ImageFile', 'PIL.ImageSequence', 'PIL.ImageShow', 'PIL.ImageWin', 'PIL.ImageGrab',
    'PIL.ImagePath', 'PIL.ImageQt', 'PIL.ImageCms', 'PIL.ImageMath', 'PIL.ImageMode',
    'PIL.ImagePalette', 'PIL.ImageTransform', 'PIL._imaging', 'PIL._imagingft',
    'PIL._imagingmath', 'PIL._imagingmorph', 'PIL._imagingcms', 'PIL._webp', 'PIL._tkinter_finder',
    'ntwork', 'ntwork.core', 'ntwork.core.wework', 'ntwork.core.mgr', 'ntwork.utils',
    'ntwork.utils.logger', 'ntwork.utils.singleton', 'ntwork.utils.xdg', 'ntwork.const',
    'ntwork.const.notify_type', 'ntwork.const.send_type', 'ntwork.exception', 'ntwork.wc',
    'pilk', 'pyee', 'pyee.base', 'pyee.asyncio', 'websockets', 'websockets.client',
    'websockets.server', 'aiohttp', 'aiohttp.client', 'aiohttp.web',
    'gui.main_window', 'gui.controllers', 'gui.config_loader', 'gui.managers',
    'gui.system_tray', 'gui.help_window', 'gui.donate_window', 'config',
    'common.log', 'common.utils', 'common.singleton', 'common.embedded_resources',
    'bridge.bridge', 'bridge.context', 'bridge.reply', 'bot.bot_factory',
    'channel.channel_factory', 'plugins.plugin_manager',
    'requests', 'json', 'threading', 'queue', 'sqlite3', 'configparser', 'logging',
    'ctypes', 'ctypes.wintypes', 'win32api', 'win32con', 'win32gui', 'pywintypes',
    'asyncio', 'typing_extensions', 'dataclasses', 'base64', 'tempfile',
]

excludes = ['matplotlib', 'scipy', 'tensorflow', 'torch', 'tkinter', 'pygame', 'voice']

block_cipher = None

a = Analysis(
    ['gui_app.py'],
    pathex=[project_root],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=excludes,
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

# 图标文件路径
icon_path = os.path.join(project_root, '2.ico')

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='企业微信自动回复系统_终极解决版',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=False,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=icon_path if os.path.exists(icon_path) else None,
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=False,
    upx_exclude=[],
    name='企业微信自动回复系统_终极解决版',
)
'''
    
    with open('ultimate_solution.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✅ 终极解决方案spec文件创建完成")

def main():
    """主函数"""
    print("🚀 企业微信自动回复系统 - 终极解决方案")
    print("=" * 60)
    print("💡 解决思路: 将图片数据直接嵌入代码，完全避免路径问题")
    print("=" * 60)
    
    # 创建嵌入式资源
    create_embedded_resources()
    
    # 修复所有模块
    fix_all_modules()
    
    # 创建终极spec文件
    create_ultimate_spec()
    
    print("\n" + "=" * 60)
    print("🎉 终极解决方案准备完成！")
    print("=" * 60)
    print("💡 执行步骤:")
    print("1. 运行: pack_env\\Scripts\\python.exe -m PyInstaller ultimate_solution.spec")
    print("2. 运行: python fix_qt_plugins.py")
    print("3. 测试: 运行生成的exe文件")
    print("\n✅ 核心优势:")
    print("   - 图片数据直接嵌入代码，无需外部文件")
    print("   - 完全避免PyInstaller路径解析问题")
    print("   - 运行时动态创建临时文件")
    print("   - 程序退出时自动清理临时文件")
    print("   - 100%可靠的图标和图片显示")

if __name__ == "__main__":
    main()
