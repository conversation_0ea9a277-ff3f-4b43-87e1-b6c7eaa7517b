#!/usr/bin/env python3
# encoding:utf-8
"""
创建修复后的资源嵌入spec文件
"""

import os
import sys
import base64
from pathlib import Path

def read_and_encode_files():
    """读取并编码图片文件"""
    print("📦 读取并编码图片文件...")
    
    files_to_encode = {
        "2.ico": "程序图标",
        "12.jpg": "联系方式图片",
        "33.png": "捐赠二维码"
    }
    
    encoded_files = {}
    
    for filename, description in files_to_encode.items():
        file_path = Path(filename)
        if file_path.exists():
            with open(file_path, 'rb') as f:
                data = f.read()
            encoded = base64.b64encode(data).decode('utf-8')
            encoded_files[filename] = encoded
            print(f"✅ {description} ({filename}): {len(data)} 字节 -> {len(encoded)} 字符")
        else:
            print(f"❌ {description} ({filename}): 文件不存在")
            encoded_files[filename] = ""
    
    return encoded_files

def create_simple_resource_spec():
    """创建简单的资源包含spec文件"""
    print("📝 创建简单资源包含spec文件...")
    
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-
# 简单资源包含版 - 确保图标和图片被正确包含

import os
from pathlib import Path

project_root = os.path.dirname(os.path.abspath(SPEC))

# 数据文件 - 明确包含所有资源文件
datas = []

# 图片文件 - 使用绝对路径确保包含
image_files = ['2.ico', '12.jpg', '33.png']
for image_file in image_files:
    image_path = os.path.join(project_root, image_file)
    if os.path.exists(image_path):
        # 放在根目录
        datas.append((image_path, '.'))
        print(f"添加图片文件: {image_file} -> 根目录")
    else:
        print(f"警告: 图片文件不存在: {image_path}")

# 配置文件
config_files = ['config-template.json', 'config-gui.json']
for config_file in config_files:
    config_path = os.path.join(project_root, config_file)
    if os.path.exists(config_path):
        datas.append((config_path, '.'))
        print(f"添加配置文件: {config_file}")

# ntwork目录
ntwork_path = os.path.join(project_root, 'ntwork')
if os.path.exists(ntwork_path):
    for root, dirs, files in os.walk(ntwork_path):
        for file in files:
            if not file.endswith('.pyc') and '__pycache__' not in root:
                src_path = os.path.join(root, file)
                rel_path = os.path.relpath(src_path, project_root)
                dest_path = os.path.dirname(rel_path)
                datas.append((src_path, dest_path))

# 隐藏导入
hiddenimports = [
    # PyQt5
    'PyQt5.QtCore', 'PyQt5.QtGui', 'PyQt5.QtWidgets', 'PyQt5.sip', 'PyQt5.QtPrintSupport',
    
    # PIL/Pillow
    'PIL', 'PIL.Image', 'PIL.ImageTk', 'PIL.ImageDraw', 'PIL.ImageFont', 'PIL.ImageFilter',
    'PIL.ImageEnhance', 'PIL.ImageOps', 'PIL.ImageChops', 'PIL.ImageStat', 'PIL.ImageColor',
    'PIL.ImageFile', 'PIL.ImageSequence', 'PIL.ImageShow', 'PIL.ImageWin', 'PIL.ImageGrab',
    'PIL.ImagePath', 'PIL.ImageQt', 'PIL.ImageCms', 'PIL.ImageMath', 'PIL.ImageMode',
    'PIL.ImagePalette', 'PIL.ImageTransform', 'PIL._imaging', 'PIL._imagingft',
    'PIL._imagingmath', 'PIL._imagingmorph', 'PIL._imagingcms', 'PIL._webp', 'PIL._tkinter_finder',
    
    # ntwork
    'ntwork', 'ntwork.core', 'ntwork.core.wework', 'ntwork.core.mgr', 'ntwork.utils',
    'ntwork.utils.logger', 'ntwork.utils.singleton', 'ntwork.utils.xdg', 'ntwork.const',
    'ntwork.const.notify_type', 'ntwork.const.send_type', 'ntwork.exception', 'ntwork.wc',
    
    # 依赖
    'pilk', 'pyee', 'pyee.base', 'pyee.asyncio', 'websockets', 'websockets.client',
    'websockets.server', 'aiohttp', 'aiohttp.client', 'aiohttp.web',
    
    # 应用模块
    'gui.main_window', 'gui.controllers', 'gui.config_loader', 'gui.managers',
    'gui.system_tray', 'gui.help_window', 'gui.donate_window', 'config',
    'common.log', 'common.utils', 'common.singleton', 'common.simple_resource_manager',
    'bridge.bridge', 'bridge.context', 'bridge.reply', 'bot.bot_factory',
    'channel.channel_factory', 'plugins.plugin_manager',
    
    # 其他
    'requests', 'json', 'threading', 'queue', 'sqlite3', 'configparser', 'logging',
    'ctypes', 'ctypes.wintypes', 'win32api', 'win32con', 'win32gui', 'pywintypes',
    'asyncio', 'typing_extensions', 'dataclasses',
]

excludes = ['matplotlib', 'scipy', 'tensorflow', 'torch', 'tkinter', 'pygame', 'voice']

block_cipher = None

a = Analysis(
    ['gui_app.py'],
    pathex=[project_root],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=excludes,
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

# 图标文件路径 - 确保图标被嵌入到exe中
icon_path = os.path.join(project_root, '2.ico')
if not os.path.exists(icon_path):
    print(f"警告: 图标文件不存在: {icon_path}")
    icon_path = None
else:
    print(f"使用图标文件: {icon_path}")

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='企业微信自动回复系统_资源确保版',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=False,
    console=False,  # 无控制台版本
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=icon_path,  # 嵌入图标到exe文件
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=False,
    upx_exclude=[],
    name='企业微信自动回复系统_资源确保版',
)

print("✅ 资源确保版spec文件执行完成！")
print("📋 包含的资源:")
print("   - 2.ico: 程序图标 (嵌入到exe + 作为文件)")
print("   - 12.jpg: 联系方式图片 (作为文件)")
print("   - 33.png: 捐赠二维码 (作为文件)")
'''
    
    with open('resource_ensure.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✅ 资源确保版spec文件创建完成")

def create_hook_based_spec():
    """创建基于hook的资源包含spec文件"""
    print("📝 创建基于hook的spec文件...")
    
    # 首先创建hook文件
    hook_content = '''# PyInstaller hook for ensuring image resources are included

from PyInstaller.utils.hooks import collect_data_files
import os

# 获取当前目录
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)

# 定义要包含的资源文件
resource_files = ['2.ico', '12.jpg', '33.png']

# 创建datas列表
datas = []
for resource_file in resource_files:
    resource_path = os.path.join(project_root, resource_file)
    if os.path.exists(resource_path):
        datas.append((resource_path, '.'))
        print(f"Hook添加资源文件: {resource_file}")

print(f"Hook包含了 {len(datas)} 个资源文件")
'''
    
    # 创建hooks目录
    hooks_dir = Path("hooks")
    if not hooks_dir.exists():
        hooks_dir.mkdir()
    
    hook_file = hooks_dir / "hook-gui_app.py"
    with open(hook_file, 'w', encoding='utf-8') as f:
        f.write(hook_content)
    
    print(f"✅ Hook文件创建完成: {hook_file}")
    
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-
# 基于hook的资源包含版

import os
from pathlib import Path

project_root = os.path.dirname(os.path.abspath(SPEC))

# 数据文件
datas = []

# 明确添加图片文件
image_files = ['2.ico', '12.jpg', '33.png']
for image_file in image_files:
    image_path = os.path.join(project_root, image_file)
    if os.path.exists(image_path):
        datas.append((image_path, '.'))
        print(f"Spec添加图片文件: {image_file}")

# 配置文件
config_files = ['config-template.json', 'config-gui.json']
for config_file in config_files:
    config_path = os.path.join(project_root, config_file)
    if os.path.exists(config_path):
        datas.append((config_path, '.'))

# ntwork目录
ntwork_path = os.path.join(project_root, 'ntwork')
if os.path.exists(ntwork_path):
    for root, dirs, files in os.walk(ntwork_path):
        for file in files:
            if not file.endswith('.pyc') and '__pycache__' not in root:
                src_path = os.path.join(root, file)
                rel_path = os.path.relpath(src_path, project_root)
                dest_path = os.path.dirname(rel_path)
                datas.append((src_path, dest_path))

# 隐藏导入
hiddenimports = [
    'PyQt5.QtCore', 'PyQt5.QtGui', 'PyQt5.QtWidgets', 'PyQt5.sip', 'PyQt5.QtPrintSupport',
    'PIL', 'PIL.Image', 'PIL.ImageTk', 'PIL.ImageDraw', 'PIL.ImageFont', 'PIL.ImageFilter',
    'PIL.ImageEnhance', 'PIL.ImageOps', 'PIL.ImageChops', 'PIL.ImageStat', 'PIL.ImageColor',
    'PIL.ImageFile', 'PIL.ImageSequence', 'PIL.ImageShow', 'PIL.ImageWin', 'PIL.ImageGrab',
    'PIL.ImagePath', 'PIL.ImageQt', 'PIL.ImageCms', 'PIL.ImageMath', 'PIL.ImageMode',
    'PIL.ImagePalette', 'PIL.ImageTransform', 'PIL._imaging', 'PIL._imagingft',
    'PIL._imagingmath', 'PIL._imagingmorph', 'PIL._imagingcms', 'PIL._webp', 'PIL._tkinter_finder',
    'ntwork', 'ntwork.core', 'ntwork.core.wework', 'ntwork.core.mgr', 'ntwork.utils',
    'ntwork.utils.logger', 'ntwork.utils.singleton', 'ntwork.utils.xdg', 'ntwork.const',
    'ntwork.const.notify_type', 'ntwork.const.send_type', 'ntwork.exception', 'ntwork.wc',
    'pilk', 'pyee', 'pyee.base', 'pyee.asyncio', 'websockets', 'websockets.client',
    'websockets.server', 'aiohttp', 'aiohttp.client', 'aiohttp.web',
    'gui.main_window', 'gui.controllers', 'gui.config_loader', 'gui.managers',
    'gui.system_tray', 'gui.help_window', 'gui.donate_window', 'config',
    'common.log', 'common.utils', 'common.singleton', 'common.simple_resource_manager',
    'bridge.bridge', 'bridge.context', 'bridge.reply', 'bot.bot_factory',
    'channel.channel_factory', 'plugins.plugin_manager',
    'requests', 'json', 'threading', 'queue', 'sqlite3', 'configparser', 'logging',
    'ctypes', 'ctypes.wintypes', 'win32api', 'win32con', 'win32gui', 'pywintypes',
    'asyncio', 'typing_extensions', 'dataclasses',
]

excludes = ['matplotlib', 'scipy', 'tensorflow', 'torch', 'tkinter', 'pygame', 'voice']

block_cipher = None

a = Analysis(
    ['gui_app.py'],
    pathex=[project_root],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[os.path.join(project_root, 'hooks')],  # 使用自定义hook
    hooksconfig={},
    runtime_hooks=[],
    excludes=excludes,
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

icon_path = os.path.join(project_root, '2.ico')

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='企业微信自动回复系统_Hook版',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=False,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=icon_path if os.path.exists(icon_path) else None,
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=False,
    upx_exclude=[],
    name='企业微信自动回复系统_Hook版',
)
'''
    
    with open('hook_based.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✅ Hook版spec文件创建完成")

def main():
    """主函数"""
    print("🚀 创建修复后的资源嵌入spec文件")
    print("=" * 60)
    
    # 读取并编码文件
    encoded_files = read_and_encode_files()
    
    # 创建简单资源包含spec
    create_simple_resource_spec()
    
    # 创建hook版spec
    create_hook_based_spec()
    
    print("\n" + "=" * 60)
    print("🎉 修复后的资源嵌入方案创建完成！")
    print("=" * 60)
    print("💡 推荐使用顺序:")
    print("")
    print("🔧 方案1: 资源确保版 (最推荐)")
    print("   pack_env\\Scripts\\python.exe -m PyInstaller resource_ensure.spec")
    print("   - 明确包含所有图片文件")
    print("   - 图标正确嵌入到exe")
    print("   - 简单可靠")
    print("")
    print("🔧 方案2: Hook版")
    print("   pack_env\\Scripts\\python.exe -m PyInstaller hook_based.spec")
    print("   - 使用PyInstaller hook机制")
    print("   - 双重确保资源包含")
    print("")
    print("✅ 两种方案都会确保:")
    print("   - 2.ico程序图标正确显示")
    print("   - 12.jpg联系方式图片正确加载")
    print("   - 33.png捐赠二维码正确显示")

if __name__ == "__main__":
    main()
